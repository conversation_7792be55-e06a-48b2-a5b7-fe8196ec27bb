-- Initial schema creation for Crypto Java API
-- This migration creates all the core tables with proper indexes and constraints

-- Create cryptocurrencies table
CREATE TABLE IF NOT EXISTS cryptocurrencies (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    symbol VARCHAR(50) NOT NULL UNIQUE,
    price DECIMAL(20, 8),
    change_24h DECIMAL(10, 4),
    market_cap DECIMAL(30, 2),
    volume_24h DECIMAL(30, 2),
    color VARCHAR(50),
    bg_color VARCHAR(50),
    border_color VARCHAR(50),
    image_url TEXT,
    
    -- Additional fields from external APIs
    id_str VARCHAR(255),
    rank_position INTEGER,
    price_btc DECIMAL(20, 8),
    available_supply DECIMAL(30, 2),
    total_supply DECIMAL(30, 2),
    max_supply DECIMAL(30, 2),
    percent_change_1h DECIMAL(10, 4),
    percent_change_7d DECIMAL(10, 4),
    last_updated_timestamp BIGINT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for cryptocurrencies table
CREATE INDEX IF NOT EXISTS idx_crypto_name ON cryptocurrencies(name);
CREATE INDEX IF NOT EXISTS idx_crypto_symbol ON cryptocurrencies(symbol);
CREATE INDEX IF NOT EXISTS idx_crypto_market_cap_desc ON cryptocurrencies(market_cap DESC);
CREATE INDEX IF NOT EXISTS idx_crypto_price_change ON cryptocurrencies(price, change_24h);
CREATE INDEX IF NOT EXISTS idx_crypto_last_updated ON cryptocurrencies(last_updated DESC);
CREATE INDEX IF NOT EXISTS idx_crypto_volume_desc ON cryptocurrencies(volume_24h DESC);
CREATE INDEX IF NOT EXISTS idx_crypto_rank ON cryptocurrencies(rank_position);

-- Create articles table
CREATE TABLE IF NOT EXISTS articles (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(500) NOT NULL UNIQUE,
    guid VARCHAR(500) UNIQUE,
    summary TEXT,
    category VARCHAR(100),
    source VARCHAR(255),
    source_url TEXT,
    image_url TEXT,
    published_date TIMESTAMP WITH TIME ZONE,
    body JSONB,
    key_takeaways JSONB,
    related_links JSONB,
    author VARCHAR(255),
    read_time VARCHAR(50),
    
    -- AI-generated structured summary fields
    headline VARCHAR(500),
    sections JSONB,
    tags JSONB,
    highlighted_coins JSONB,
    tone VARCHAR(100),
    sentiment VARCHAR(50),
    hot_score INTEGER CHECK (hot_score >= 0 AND hot_score <= 100),
    summaries JSONB,
    raw_llm_response TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for articles table
CREATE INDEX IF NOT EXISTS idx_article_title ON articles(title);
CREATE INDEX IF NOT EXISTS idx_article_slug ON articles(slug);
CREATE INDEX IF NOT EXISTS idx_article_guid ON articles(guid);
CREATE INDEX IF NOT EXISTS idx_article_category ON articles(category);
CREATE INDEX IF NOT EXISTS idx_article_published_date_desc ON articles(published_date DESC);
CREATE INDEX IF NOT EXISTS idx_article_category_date ON articles(category, published_date DESC);
CREATE INDEX IF NOT EXISTS idx_article_hot_score_date ON articles(hot_score DESC, published_date DESC);
CREATE INDEX IF NOT EXISTS idx_article_sentiment_date ON articles(sentiment, published_date DESC);
CREATE INDEX IF NOT EXISTS idx_article_created_at ON articles(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_article_updated_at ON articles(updated_at DESC);

-- Create GIN indexes for JSONB columns (PostgreSQL specific)
CREATE INDEX IF NOT EXISTS idx_article_highlighted_coins_gin ON articles USING GIN(highlighted_coins);
CREATE INDEX IF NOT EXISTS idx_article_tags_gin ON articles USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_article_sections_gin ON articles USING GIN(sections);
CREATE INDEX IF NOT EXISTS idx_article_body_gin ON articles USING GIN(body);

-- Create crypto_cache table
CREATE TABLE IF NOT EXISTS crypto_cache (
    id BIGSERIAL PRIMARY KEY,
    cache_key VARCHAR(500) NOT NULL UNIQUE,
    data JSONB NOT NULL,
    etag VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create indexes for crypto_cache table
CREATE INDEX IF NOT EXISTS idx_cache_key ON crypto_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_cache_expires_at ON crypto_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_cache_etag ON crypto_cache(etag);
CREATE INDEX IF NOT EXISTS idx_cache_key_expires ON crypto_cache(cache_key, expires_at);
CREATE INDEX IF NOT EXISTS idx_cache_created_at ON crypto_cache(created_at);

-- Create index for expired cache entries (for cleanup operations)
CREATE INDEX IF NOT EXISTS idx_expired_cache ON crypto_cache(expires_at);

-- Create functional index for cache key prefix (for grouped cache invalidation)
CREATE INDEX IF NOT EXISTS idx_cache_key_prefix ON crypto_cache(split_part(cache_key, ':', 1));

-- Create trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_cryptocurrencies_updated_at 
    BEFORE UPDATE ON cryptocurrencies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_articles_updated_at 
    BEFORE UPDATE ON articles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE cryptocurrencies IS 'Stores cryptocurrency market data and metadata';
COMMENT ON TABLE articles IS 'Stores cryptocurrency news articles with AI-generated summaries';
COMMENT ON TABLE crypto_cache IS 'Caches API responses and computed data with TTL support';

COMMENT ON COLUMN cryptocurrencies.symbol IS 'Unique cryptocurrency symbol (e.g., BTC, ETH)';
COMMENT ON COLUMN cryptocurrencies.price IS 'Current price in USD';
COMMENT ON COLUMN cryptocurrencies.change_24h IS '24-hour price change percentage';
COMMENT ON COLUMN cryptocurrencies.market_cap IS 'Market capitalization in USD';
COMMENT ON COLUMN cryptocurrencies.volume_24h IS '24-hour trading volume in USD';

COMMENT ON COLUMN articles.slug IS 'SEO-friendly URL slug generated from title';
COMMENT ON COLUMN articles.guid IS 'Unique identifier from RSS feed';
COMMENT ON COLUMN articles.sentiment IS 'AI-analyzed sentiment: bullish, bearish, or neutral';
COMMENT ON COLUMN articles.hot_score IS 'Trending score from 0-100 based on various factors';
COMMENT ON COLUMN articles.highlighted_coins IS 'JSON array of cryptocurrency symbols mentioned';
COMMENT ON COLUMN articles.sections IS 'JSON array of structured article sections';

COMMENT ON COLUMN crypto_cache.cache_key IS 'Unique cache identifier with namespace prefix';
COMMENT ON COLUMN crypto_cache.etag IS 'HTTP ETag for cache validation';
COMMENT ON COLUMN crypto_cache.expires_at IS 'Cache expiration timestamp';

-- Create sequences if they don't exist (for compatibility)
CREATE SEQUENCE IF NOT EXISTS cryptocurrencies_id_seq OWNED BY cryptocurrencies.id;
CREATE SEQUENCE IF NOT EXISTS articles_id_seq OWNED BY articles.id;
CREATE SEQUENCE IF NOT EXISTS crypto_cache_id_seq OWNED BY crypto_cache.id;
