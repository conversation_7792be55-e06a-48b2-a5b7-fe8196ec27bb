# Development Profile Configuration
spring:
  # Neon PostgreSQL Database for development
  r2dbc:
    url: ${R2DBC_URL:r2dbc:postgresql://ep-orange-pond-a27eado1-pooler.eu-central-1.aws.neon.tech:5432/neondb?sslmode=require&project=ep-orange-pond-a27eado1}
    username: ${DB_USERNAME:neondb_owner}
    password: ${DB_PASSWORD:npg_okyEf7ZOJ1Ui}
    pool:
      initial-size: 5
      max-size: 10
      max-idle-time: 1800s
      max-acquire-time: 30s
      max-life-time: 3600s
      validation-query: SELECT 1

  # Disable H2 Console for PostgreSQL
  h2:
    console:
      enabled: false

  # Enable Flyway for PostgreSQL migrations
  flyway:
    enabled: true
    url: ${FLYWAY_URL:******************************************************************************************************************************}
    user: ${DB_USERNAME:neondb_owner}
    password: ${DB_PASSWORD:npg_okyEf7ZOJ1Ui}
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
  
  # Redis Configuration for Development
  data:
    redis:
      url: redis://localhost:6379
      ssl:
        enabled: false

# Logging Configuration for Development
logging:
  level:
    com.cryptoapp: DEBUG
    org.springframework.r2dbc: DEBUG
    org.springframework.data.r2dbc: DEBUG
    org.springframework.web.reactive: DEBUG
    reactor.netty.http: DEBUG
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"

# Management endpoints for development
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

# Performance settings for development
performance:
  max-concurrent-tasks: 3
  cache:
    ttl:
      market-data: 30  # Shorter TTL for development
      articles: 1800   # 30 minutes
      ohlc: 120       # 2 minutes

# Scheduling intervals for development (shorter for testing)
scheduling:
  article-sync-interval: 300000    # 5 minutes
  crypto-sync-interval: 30000      # 30 seconds
  ohlc-cache-warm-interval: 120000 # 2 minutes
  cache-cleanup-interval: 600000   # 10 minutes

# CORS for development
cors:
  allowed-origins: "http://localhost:3000,http://localhost:3001,http://localhost:8080"

# Circuit breaker settings for development (more lenient)
resilience4j:
  circuitbreaker:
    instances:
      llm-provider:
        failure-rate-threshold: 70
        wait-duration-in-open-state: 10s
      external-api:
        failure-rate-threshold: 70
        wait-duration-in-open-state: 30s

# LLM Configuration for Development
llm:
  provider: local
  local:
    enabled: true
    base-url: http://localhost:1234/v1
    model: local-model
    temperature: 0.7

# External API Configuration for Development
external:
  api:
    provider: alternative  # Use Kraken for real data in development
