-- Create remaining tables from the original schema
-- Note: articles table is already created in V1, so we only create additional tables here

-- Create article_tags table for many-to-many relationship
CREATE TABLE IF NOT EXISTS article_tags (
    id BIGSERIAL PRIMARY KEY,
    article_id BIGINT NOT NULL,
    tag VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_article_tags_article FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
    CONSTRAINT uk_article_tag UNIQUE (article_id, tag)
);

-- Create article_highlighted_coins table for many-to-many relationship
CREATE TABLE IF NOT EXISTS article_highlighted_coins (
    id BIGSERIAL PRIMARY KEY,
    article_id BIGINT NOT NULL,
    cryptocurrency_symbol VARCHAR(50) NOT NULL,
    relevance_score DECIMAL(3, 2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_article_coins_article FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
    CONSTRAINT uk_article_coin UNIQUE (article_id, cryptocurrency_symbol)
);

-- Create article_summaries table for AI-generated summaries
CREATE TABLE IF NOT EXISTS article_summaries (
    id BIGSERIAL PRIMARY KEY,
    article_id BIGINT NOT NULL,
    summary_type VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    model_used VARCHAR(100),
    confidence_score DECIMAL(3, 2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_article_summaries_article FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
    CONSTRAINT uk_article_summary_type UNIQUE (article_id, summary_type)
);

-- Create crypto_cache table for caching API responses
CREATE TABLE IF NOT EXISTS crypto_cache (
    id BIGSERIAL PRIMARY KEY,
    cache_key VARCHAR(500) NOT NULL UNIQUE,
    cache_value TEXT NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for articles table
CREATE INDEX IF NOT EXISTS idx_articles_title ON articles(title);
CREATE INDEX IF NOT EXISTS idx_articles_slug ON articles(slug);
CREATE INDEX IF NOT EXISTS idx_articles_category ON articles(category);
CREATE INDEX IF NOT EXISTS idx_articles_published_date_desc ON articles(published_date DESC);
CREATE INDEX IF NOT EXISTS idx_articles_sentiment ON articles(sentiment);
CREATE INDEX IF NOT EXISTS idx_articles_hot_score_desc ON articles(hot_score DESC);
CREATE INDEX IF NOT EXISTS idx_articles_created_at_desc ON articles(created_at DESC);

-- Create indexes for article_tags table
CREATE INDEX IF NOT EXISTS idx_article_tags_article_id ON article_tags(article_id);
CREATE INDEX IF NOT EXISTS idx_article_tags_tag ON article_tags(tag);

-- Create indexes for article_highlighted_coins table
CREATE INDEX IF NOT EXISTS idx_article_coins_article_id ON article_highlighted_coins(article_id);
CREATE INDEX IF NOT EXISTS idx_article_coins_symbol ON article_highlighted_coins(cryptocurrency_symbol);
CREATE INDEX IF NOT EXISTS idx_article_coins_relevance_desc ON article_highlighted_coins(relevance_score DESC);

-- Create indexes for article_summaries table
CREATE INDEX IF NOT EXISTS idx_article_summaries_article_id ON article_summaries(article_id);
CREATE INDEX IF NOT EXISTS idx_article_summaries_type ON article_summaries(summary_type);

-- Create indexes for crypto_cache table
CREATE INDEX IF NOT EXISTS idx_cache_key ON crypto_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_cache_expires_at ON crypto_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_cache_last_accessed ON crypto_cache(last_accessed);

-- Create functional index for cache cleanup
CREATE INDEX IF NOT EXISTS idx_cache_key_prefix ON crypto_cache(split_part(cache_key, ':', 1));

-- Add comments for documentation
COMMENT ON TABLE cryptocurrencies IS 'Stores cryptocurrency market data and metadata';
COMMENT ON TABLE articles IS 'Stores cryptocurrency news articles with AI-generated summaries';
COMMENT ON TABLE crypto_cache IS 'Caches API responses and computed data with TTL support';

COMMENT ON COLUMN cryptocurrencies.symbol IS 'Unique cryptocurrency symbol (e.g., BTC, ETH)';
COMMENT ON COLUMN cryptocurrencies.price IS 'Current price in USD';
COMMENT ON COLUMN cryptocurrencies.change_24h IS '24-hour price change percentage';
COMMENT ON COLUMN cryptocurrencies.market_cap IS 'Market capitalization in USD';
COMMENT ON COLUMN cryptocurrencies.volume_24h IS '24-hour trading volume in USD';
