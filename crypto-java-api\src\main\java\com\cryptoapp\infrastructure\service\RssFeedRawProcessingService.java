package com.cryptoapp.infrastructure.service;

import com.cryptoapp.domain.RssFeedRaw;
import com.cryptoapp.infrastructure.repository.RssFeedRawRepository;
import com.cryptoapp.port.out.RssFeedPort;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.r2dbc.postgresql.codec.Json;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

/**
 * Clean Stage 1 RSS Raw Data Processor.
 *
 * This service handles the first stage of the two-stage RSS processing pipeline:
 * - Fetches RSS data from all configured sources using RssFeedPort
 * - Normalizes and cleans the raw RSS data
 * - Performs duplicate prevention using GUID and content hash checks
 * - Stores normalized raw data in rss_feed_raw table for Stage 2 LLM processing
 *
 * Design principles:
 * - Single responsibility: Only handles raw data collection and normalization
 * - No LLM processing (handled by Stage 2)
 * - Comprehensive logging for debugging
 * - Proper error handling and metrics
 * - Industry-standard reactive patterns
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RssFeedRawProcessingService {

    private final RssFeedRawRepository rssFeedRawRepository;
    private final RssFeedPort rssFeedPort;
    private final ObjectMapper objectMapper;

    // Processing metrics
    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger duplicateCount = new AtomicInteger(0);
    private final AtomicInteger errorCount = new AtomicInteger(0);
    private final AtomicInteger skippedCount = new AtomicInteger(0);

    // Content normalization patterns
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s+");
    private static final Pattern SPECIAL_CHARS_PATTERN = Pattern.compile("[\\r\\n\\t]+");

    // Feed priority mapping
    private static final Map<String, Integer> FEED_PRIORITIES = Map.of(
        "cointelegraph.com", 1,
        "coindesk.com", 1,
        "decrypt.co", 1,
        "theblockcrypto.com", 1,
        "bitcoinmagazine.com", 2,
        "cryptonews.com", 2,
        "cryptopotato.com", 2,
        "cryptoslate.com", 2,
        "beincrypto.com", 3,
        "ambcrypto.com", 3
    );

    /**
     * Main entry point: Process RSS feeds from all sources and store raw data.
     * This is the primary method called by the Stage 1 scheduler.
     */
    public Mono<Integer> processAllRssFeeds(int limit) {
        log.info("=== Starting Stage 1 RSS Raw Data Processing ===");
        log.info("Processing limit: {} articles", limit);

        resetCounters();
        long startTime = System.currentTimeMillis();

        return rssFeedPort.fetchArticlesFromAllFeeds(limit)
                .doOnNext(article -> log.debug("Fetched RSS article: {} from {}",
                    article.getTitle(), article.getSource()))
                .flatMap(this::processRssArticle)
                .collectList()
                .map(results -> {
                    long processingTime = System.currentTimeMillis() - startTime;
                    int totalProcessed = processedCount.get();
                    int totalDuplicates = duplicateCount.get();
                    int totalErrors = errorCount.get();
                    int totalSkipped = skippedCount.get();

                    log.info("=== Stage 1 RSS Processing Completed ===");
                    log.info("Processing time: {}ms", processingTime);
                    log.info("Articles processed: {}", totalProcessed);
                    log.info("Duplicates skipped: {}", totalDuplicates);
                    log.info("Errors encountered: {}", totalErrors);
                    log.info("Articles skipped: {}", totalSkipped);

                    return totalProcessed;
                })
                .doOnError(error -> {
                    log.error("=== Stage 1 RSS Processing Failed ===", error);
                    errorCount.incrementAndGet();
                });
    }

    /**
     * Reset all processing counters for a new processing run.
     */
    private void resetCounters() {
        processedCount.set(0);
        duplicateCount.set(0);
        errorCount.set(0);
        skippedCount.set(0);
        log.debug("Processing counters reset");
    }

    /**
     * Process a single RSS article through the Stage 1 pipeline.
     */
    private Mono<RssFeedRaw> processRssArticle(RssFeedPort.RssArticle rssArticle) {
        return Mono.fromCallable(() -> rssArticle)
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(this::checkForDuplicates)
                .flatMap(this::normalizeAndCreateRawEntity)
                .flatMap(this::saveRawEntity)
                .doOnNext(saved -> processedCount.incrementAndGet())
                .doOnError(error -> {
                    errorCount.incrementAndGet();
                    log.error("Error processing RSS article: {}", rssArticle.getTitle(), error);
                })
                .onErrorResume(error -> Mono.empty()); // Continue processing other articles
    }

    /**
     * Check for duplicates using GUID and content hash.
     */
    private Mono<RssFeedPort.RssArticle> checkForDuplicates(RssFeedPort.RssArticle rssArticle) {
        if (rssArticle.getGuid() == null || rssArticle.getGuid().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("RSS article GUID is required"));
        }

        return rssFeedRawRepository.existsByGuid(rssArticle.getGuid())
                .flatMap(exists -> {
                    if (exists) {
                        duplicateCount.incrementAndGet();
                        log.debug("Duplicate article found by GUID: {}", rssArticle.getGuid());
                        return Mono.empty();
                    }
                    return Mono.just(rssArticle);
                });
    }

    /**
     * Normalize RSS data and create RssFeedRaw entity.
     */
    private Mono<RssFeedRaw> normalizeAndCreateRawEntity(RssFeedPort.RssArticle rssArticle) {
        return Mono.fromCallable(() -> {
            // Normalize content
            String normalizedTitle = normalizeText(rssArticle.getTitle());
            String normalizedDescription = normalizeText(rssArticle.getDescription());
            String normalizedContent = normalizeText(rssArticle.getContent());

            // Create normalized data JSON
            Map<String, Object> normalizedData = createNormalizedDataMap(rssArticle, 
                    normalizedTitle, normalizedDescription, normalizedContent);

            // Create raw RSS data JSON
            Map<String, Object> rawRssData = createRawRssDataMap(rssArticle);

            // Build RssFeedRaw entity
            RssFeedRaw rawEntity = RssFeedRaw.builder()
                    .guid(rssArticle.getGuid())
                    .source(rssArticle.getSource())
                    .sourceUrl(rssArticle.getLink())
                    .title(normalizedTitle)
                    .description(normalizedDescription)
                    .content(normalizedContent)
                    .author(rssArticle.getAuthor())
                    .category(rssArticle.getCategory())
                    .imageUrl(rssArticle.getImageUrl())
                    .link(rssArticle.getLink())
                    .publishedDate(rssArticle.getPublishedDate())
                    .fetchedAt(LocalDateTime.now())
                    .processingStatus(RssFeedRaw.ProcessingStatus.PENDING)
                    .feedPriority(determineFeedPriority(rssArticle.getSource()))
                    .rawRssData(Json.of(objectMapper.writeValueAsString(rawRssData)))
                    .normalizedData(Json.of(objectMapper.writeValueAsString(normalizedData)))
                    .build();

            // Generate content hash for deduplication
            rawEntity.generateContentHash();

            return rawEntity;
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnNext(entity -> log.debug("Normalized RSS article: {} from {}", 
                entity.getTitle(), entity.getSource()));
    }

    /**
     * Save the raw entity to the database.
     */
    private Mono<RssFeedRaw> saveRawEntity(RssFeedRaw rawEntity) {
        return rssFeedRawRepository.save(rawEntity)
                .doOnNext(saved -> log.debug("Saved raw RSS article: {} (ID: {})", 
                        saved.getTitle(), saved.getId()))
                .doOnError(error -> log.error("Error saving raw RSS article: {}", 
                        rawEntity.getTitle(), error));
    }

    /**
     * Normalize text content by removing HTML tags and cleaning whitespace.
     */
    private String normalizeText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }

        return text
                .replaceAll(HTML_TAG_PATTERN.pattern(), " ") // Remove HTML tags
                .replaceAll(SPECIAL_CHARS_PATTERN.pattern(), " ") // Replace special chars with space
                .replaceAll(WHITESPACE_PATTERN.pattern(), " ") // Normalize whitespace
                .trim();
    }

    /**
     * Create normalized data map for JSON storage.
     */
    private Map<String, Object> createNormalizedDataMap(RssFeedPort.RssArticle rssArticle,
                                                       String normalizedTitle,
                                                       String normalizedDescription,
                                                       String normalizedContent) {
        Map<String, Object> normalizedData = new HashMap<>();
        normalizedData.put("title", normalizedTitle);
        normalizedData.put("description", normalizedDescription);
        normalizedData.put("content", normalizedContent);
        normalizedData.put("wordCount", calculateWordCount(normalizedTitle, normalizedDescription, normalizedContent));
        normalizedData.put("hasImage", rssArticle.getImageUrl() != null && !rssArticle.getImageUrl().trim().isEmpty());
        normalizedData.put("contentLength", calculateContentLength(normalizedTitle, normalizedDescription, normalizedContent));
        normalizedData.put("normalizedAt", LocalDateTime.now().toString());
        
        return normalizedData;
    }

    /**
     * Create raw RSS data map for JSON storage.
     */
    private Map<String, Object> createRawRssDataMap(RssFeedPort.RssArticle rssArticle) {
        Map<String, Object> rawData = new HashMap<>();
        rawData.put("originalTitle", rssArticle.getTitle());
        rawData.put("originalDescription", rssArticle.getDescription());
        rawData.put("originalContent", rssArticle.getContent());
        rawData.put("link", rssArticle.getLink());
        rawData.put("author", rssArticle.getAuthor());
        rawData.put("category", rssArticle.getCategory());
        rawData.put("imageUrl", rssArticle.getImageUrl());
        rawData.put("publishedDate", rssArticle.getPublishedDate() != null ? rssArticle.getPublishedDate().toString() : null);
        rawData.put("source", rssArticle.getSource());
        rawData.put("guid", rssArticle.getGuid());
        rawData.put("tags", rssArticle.getTags());
        rawData.put("fetchedAt", LocalDateTime.now().toString());
        
        return rawData;
    }

    /**
     * Determine feed priority based on source.
     */
    private Integer determineFeedPriority(String source) {
        if (source == null) return 5;

        String lowerSource = source.toLowerCase();
        for (Map.Entry<String, Integer> entry : FEED_PRIORITIES.entrySet()) {
            if (lowerSource.contains(entry.getKey())) {
                return entry.getValue();
            }
        }

        return 5; // Default priority
    }

    /**
     * Calculate word count from normalized content.
     */
    private Integer calculateWordCount(String title, String description, String content) {
        int count = 0;

        if (title != null && !title.trim().isEmpty()) {
            count += title.trim().split("\\s+").length;
        }
        if (description != null && !description.trim().isEmpty()) {
            count += description.trim().split("\\s+").length;
        }
        if (content != null && !content.trim().isEmpty()) {
            count += content.trim().split("\\s+").length;
        }

        return count;
    }

    /**
     * Calculate total content length.
     */
    private Integer calculateContentLength(String title, String description, String content) {
        int length = 0;

        if (title != null) length += title.length();
        if (description != null) length += description.length();
        if (content != null) length += content.length();

        return length;
    }

    /**
     * Get processing statistics for monitoring.
     */
    public ProcessingStatistics getProcessingStatistics() {
        return new ProcessingStatistics(
                processedCount.get(),
                duplicateCount.get(),
                errorCount.get(),
                skippedCount.get()
        );
    }

    /**
     * Get count of articles pending LLM processing.
     */
    public Mono<Long> getPendingArticlesCount() {
        return rssFeedRawRepository.countByProcessingStatus("PENDING")
                .doOnNext(count -> log.debug("Pending articles count: {}", count));
    }

    /**
     * Get count of articles that failed processing.
     */
    public Mono<Long> getFailedArticlesCount() {
        return rssFeedRawRepository.countByProcessingStatus("FAILED")
                .doOnNext(count -> log.debug("Failed articles count: {}", count));
    }

    /**
     * Clean up old processed articles to save storage space.
     */
    public Mono<Integer> cleanupOldProcessedArticles(int daysOld) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);
        log.info("Cleaning up processed articles older than {} days (before: {})", daysOld, cutoffDate);

        return rssFeedRawRepository.deleteOldProcessedArticles(cutoffDate)
                .doOnNext(deleted -> log.info("Cleaned up {} old processed articles", deleted));
    }

    /**
     * Processing statistics record for monitoring and metrics.
     */
    public record ProcessingStatistics(int processed, int duplicates, int errors, int skipped) {}
}
