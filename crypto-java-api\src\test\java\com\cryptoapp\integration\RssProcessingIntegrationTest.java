package com.cryptoapp.integration;

import com.cryptoapp.domain.ArticleProcessed;
import com.cryptoapp.domain.RssFeedRaw;
import com.cryptoapp.infrastructure.repository.ArticleProcessedRepository;
import com.cryptoapp.infrastructure.repository.RssFeedRawRepository;
import com.cryptoapp.infrastructure.service.ArticleLlmProcessingService;
import com.cryptoapp.infrastructure.service.RssFeedRawProcessingService;
import com.cryptoapp.port.out.RssFeedPort;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for RSS processing pipeline.
 * 
 * These tests verify the complete RSS processing flow from fetching RSS feeds
 * to storing processed articles in the database, including:
 * - Stage 1: RSS feed fetching and raw data storage
 * - Stage 2: LLM processing and article creation
 * - Database persistence and querying
 * - Duplicate prevention and error handling
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.r2dbc.url=r2dbc:h2:mem:///testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.flyway.enabled=false",
    "llm.provider=mock",
    "jobs.rss-stage1.enabled=false",
    "jobs.rss-stage2.enabled=false",
    "scheduling.enabled=false"
})
class RssProcessingIntegrationTest {

    @Autowired
    private RssFeedRawProcessingService rssFeedRawProcessingService;

    @Autowired
    private ArticleLlmProcessingService articleLlmProcessingService;

    @Autowired
    private RssFeedRawRepository rssFeedRawRepository;

    @Autowired
    private ArticleProcessedRepository articleProcessedRepository;

    @Autowired
    private DatabaseClient databaseClient;

    @BeforeEach
    void setUp() {
        // Clean up database before each test
        StepVerifier.create(
            databaseClient.sql("DELETE FROM articles_processed").fetch().rowsUpdated()
                .then(databaseClient.sql("DELETE FROM rss_feed_raw").fetch().rowsUpdated())
        ).expectNextCount(1).verifyComplete();
    }

    @Test
    void shouldProcessRssFeedAndStoreRawData() {
        // Given: Mock RSS data
        RssFeedRaw mockRssData = createMockRssFeedRaw();

        // When: Save raw RSS data
        StepVerifier.create(rssFeedRawRepository.save(mockRssData))
            .assertNext(savedArticle -> {
                // Then: Verify raw data is stored correctly
                assertThat(savedArticle.getId()).isNotNull();
                assertThat(savedArticle.getGuid()).isEqualTo("test-guid-123");
                assertThat(savedArticle.getTitle()).isEqualTo("Test Bitcoin Article");
                assertThat(savedArticle.getSource()).isEqualTo("CoinDesk");
                assertThat(savedArticle.getProcessingStatus()).isEqualTo(RssFeedRaw.ProcessingStatus.PENDING);
                assertThat(savedArticle.getFetchedAt()).isNotNull();
            })
            .verifyComplete();
    }

    @Test
    void shouldPreventDuplicateRssArticles() {
        // Given: Two articles with same GUID
        RssFeedRaw article1 = createMockRssFeedRaw();
        RssFeedRaw article2 = createMockRssFeedRaw();
        article2.setTitle("Different Title");

        // When: Save first article
        StepVerifier.create(rssFeedRawRepository.save(article1))
            .expectNextCount(1)
            .verifyComplete();

        // Then: Check if duplicate exists
        StepVerifier.create(rssFeedRawRepository.existsByGuid("test-guid-123"))
            .expectNext(true)
            .verifyComplete();

        // And: Verify we can find by GUID
        StepVerifier.create(rssFeedRawRepository.findByGuid("test-guid-123"))
            .assertNext(found -> {
                assertThat(found.getTitle()).isEqualTo("Test Bitcoin Article");
                assertThat(found.getGuid()).isEqualTo("test-guid-123");
            })
            .verifyComplete();
    }

    @Test
    void shouldFindPendingArticlesForProcessing() {
        // Given: Multiple articles with different statuses
        RssFeedRaw pendingArticle1 = createMockRssFeedRaw();
        pendingArticle1.setGuid("pending-1");
        pendingArticle1.setProcessingStatus(RssFeedRaw.ProcessingStatus.PENDING);

        RssFeedRaw pendingArticle2 = createMockRssFeedRaw();
        pendingArticle2.setGuid("pending-2");
        pendingArticle2.setProcessingStatus(RssFeedRaw.ProcessingStatus.PENDING);

        RssFeedRaw processedArticle = createMockRssFeedRaw();
        processedArticle.setGuid("processed-1");
        processedArticle.setProcessingStatus(RssFeedRaw.ProcessingStatus.PROCESSED);

        // When: Save all articles
        StepVerifier.create(
            Flux.just(pendingArticle1, pendingArticle2, processedArticle)
                .flatMap(rssFeedRawRepository::save)
                .collectList()
        ).expectNextCount(1).verifyComplete();

        // Then: Find pending articles
        StepVerifier.create(rssFeedRawRepository.findPendingForProcessing(10))
            .expectNextCount(2)
            .verifyComplete();

        // And: Verify count by status
        StepVerifier.create(rssFeedRawRepository.countByProcessingStatus("PENDING"))
            .expectNext(2L)
            .verifyComplete();

        StepVerifier.create(rssFeedRawRepository.countByProcessingStatus("PROCESSED"))
            .expectNext(1L)
            .verifyComplete();
    }

    @Test
    void shouldUpdateProcessingStatus() {
        // Given: A pending article
        RssFeedRaw article = createMockRssFeedRaw();
        
        StepVerifier.create(rssFeedRawRepository.save(article))
            .assertNext(saved -> {
                Long articleId = saved.getId();
                
                // When: Mark as processing started
                StepVerifier.create(rssFeedRawRepository.markAsProcessingStarted(articleId))
                    .expectNext(1)
                    .verifyComplete();

                // Then: Verify status updated
                StepVerifier.create(rssFeedRawRepository.findById(articleId))
                    .assertNext(updated -> {
                        assertThat(updated.getProcessingStatus()).isEqualTo(RssFeedRaw.ProcessingStatus.PROCESSING);
                        assertThat(updated.getProcessingAttempts()).isEqualTo(1);
                        assertThat(updated.getLastProcessingAttempt()).isNotNull();
                    })
                    .verifyComplete();

                // When: Mark as processed
                StepVerifier.create(rssFeedRawRepository.markAsProcessed(articleId))
                    .expectNext(1)
                    .verifyComplete();

                // Then: Verify final status
                StepVerifier.create(rssFeedRawRepository.findById(articleId))
                    .assertNext(processed -> {
                        assertThat(processed.getProcessingStatus()).isEqualTo(RssFeedRaw.ProcessingStatus.PROCESSED);
                        assertThat(processed.getProcessingError()).isNull();
                    })
                    .verifyComplete();
            })
            .verifyComplete();
    }

    @Test
    void shouldFindArticlesBySource() {
        // Given: Articles from different sources
        RssFeedRaw coinDeskArticle = createMockRssFeedRaw();
        coinDeskArticle.setGuid("coindesk-1");
        coinDeskArticle.setSource("CoinDesk");

        RssFeedRaw bitcoinMagArticle = createMockRssFeedRaw();
        bitcoinMagArticle.setGuid("bitcoinmag-1");
        bitcoinMagArticle.setSource("Bitcoin Magazine");

        // When: Save articles
        StepVerifier.create(
            Flux.just(coinDeskArticle, bitcoinMagArticle)
                .flatMap(rssFeedRawRepository::save)
                .collectList()
        ).expectNextCount(1).verifyComplete();

        // Then: Find by source
        StepVerifier.create(rssFeedRawRepository.findBySource("CoinDesk", 10))
            .assertNext(found -> {
                assertThat(found.getSource()).isEqualTo("CoinDesk");
                assertThat(found.getGuid()).isEqualTo("coindesk-1");
            })
            .verifyComplete();

        StepVerifier.create(rssFeedRawRepository.countBySourceAndProcessingStatus("CoinDesk", "PENDING"))
            .expectNext(1L)
            .verifyComplete();
    }

    @Test
    void shouldFindRecentArticles() {
        // Given: Articles with different fetch times
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneHourAgo = now.minusHours(1);
        LocalDateTime twoHoursAgo = now.minusHours(2);

        RssFeedRaw recentArticle = createMockRssFeedRaw();
        recentArticle.setGuid("recent-1");
        recentArticle.setFetchedAt(now);

        RssFeedRaw oldArticle = createMockRssFeedRaw();
        oldArticle.setGuid("old-1");
        oldArticle.setFetchedAt(twoHoursAgo);

        // When: Save articles
        StepVerifier.create(
            Flux.just(recentArticle, oldArticle)
                .flatMap(rssFeedRawRepository::save)
                .collectList()
        ).expectNextCount(1).verifyComplete();

        // Then: Find recent articles
        StepVerifier.create(rssFeedRawRepository.findRecentBySource("CoinDesk", oneHourAgo, 10))
            .assertNext(found -> {
                assertThat(found.getGuid()).isEqualTo("recent-1");
                assertThat(found.getFetchedAt()).isAfter(oneHourAgo);
            })
            .verifyComplete();
    }

    private RssFeedRaw createMockRssFeedRaw() {
        return RssFeedRaw.builder()
            .guid("test-guid-123")
            .source("CoinDesk")
            .sourceUrl("https://example.com/article")
            .title("Test Bitcoin Article")
            .description("This is a test article about Bitcoin")
            .content("Full content of the test article about Bitcoin and cryptocurrency")
            .author("Test Author")
            .category("Bitcoin")
            .imageUrl("https://example.com/image.jpg")
            .link("https://example.com/article")
            .publishedDate(LocalDateTime.now().minusHours(1))
            .fetchedAt(LocalDateTime.now())
            .processingStatus(RssFeedRaw.ProcessingStatus.PENDING)
            .processingAttempts(0)
            .feedPriority(1)
            .contentHash("test-content-hash-123")
            .build();
    }
}
