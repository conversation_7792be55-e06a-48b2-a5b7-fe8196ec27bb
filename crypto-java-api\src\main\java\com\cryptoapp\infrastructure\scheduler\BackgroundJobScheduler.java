package com.cryptoapp.infrastructure.scheduler;

import com.cryptoapp.port.in.ArticleUseCase;
import com.cryptoapp.port.in.CacheUseCase;
import com.cryptoapp.port.in.CryptoCurrencyUseCase;
import com.cryptoapp.infrastructure.service.CacheWarmingService;
import com.cryptoapp.infrastructure.service.RssFeedRawProcessingService;
import com.cryptoapp.infrastructure.service.ArticleLlmProcessingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * DEPRECATED: Legacy Background Job Scheduler.
 *
 * This service has been partially replaced by the new clean ArticleProcessingScheduler.
 * Article processing methods are disabled in favor of the new clean implementation.
 * Only cryptocurrency and cache-related jobs remain active.
 *
 * @deprecated Article processing methods replaced by ArticleProcessingScheduler
 */
@Slf4j
@Service("legacyBackgroundJobScheduler")
@RequiredArgsConstructor
public class BackgroundJobScheduler {

    private final CryptoCurrencyUseCase cryptoCurrencyUseCase;
    private final ArticleUseCase articleUseCase;
    private final CacheUseCase cacheUseCase;
    private final CacheWarmingService cacheWarmingService;
    private final RssFeedRawProcessingService rssFeedRawProcessingService;
    private final ArticleLlmProcessingService articleLlmProcessingService;

    @Value("${jobs.crypto-sync.enabled:true}")
    private boolean cryptoSyncEnabled;

    @Value("${jobs.article-sync.enabled:true}")
    private boolean articleSyncEnabled;

    @Value("${jobs.cache-optimization.enabled:true}")
    private boolean cacheOptimizationEnabled;

    @Value("${jobs.data-cleanup.enabled:true}")
    private boolean dataCleanupEnabled;

    @Value("${jobs.llm-processing.enabled:true}")
    private boolean llmProcessingEnabled;

    @Value("${jobs.rss-stage1.enabled:true}")
    private boolean rssStage1Enabled;

    @Value("${jobs.rss-stage2.enabled:true}")
    private boolean rssStage2Enabled;

    @Value("${jobs.distributed-locking.enabled:true}")
    private boolean distributedLockingEnabled;

    @Value("${jobs.lock-timeout:5m}")
    private Duration lockTimeout;

    @Value("${jobs.lock-ttl:10m}")
    private Duration lockTtl;

    // Adaptive scheduling configuration
    @Value("${crypto.scheduling.base-interval:300000}")
    private long baseInterval;

    @Value("${crypto.scheduling.high-volatility-interval:60000}")
    private long highVolatilityInterval;

    @Value("${crypto.scheduling.low-volatility-interval:600000}")
    private long lowVolatilityInterval;

    @Value("${crypto.scheduling.volatility-threshold:5.0}")
    private double volatilityThreshold;

    @Value("${crypto.scheduling.volatility-check-window:24}")
    private int volatilityCheckWindow;

    // Job execution statistics
    private final AtomicLong cryptoSyncCount = new AtomicLong(0);
    private final AtomicLong articleSyncCount = new AtomicLong(0);
    private final AtomicLong cacheOptimizationCount = new AtomicLong(0);
    private final AtomicLong dataCleanupCount = new AtomicLong(0);
    private final AtomicLong llmProcessingCount = new AtomicLong(0);
    private final AtomicLong rssStage1Count = new AtomicLong(0);
    private final AtomicLong rssStage2Count = new AtomicLong(0);

    // Job status tracking
    private final AtomicBoolean cryptoSyncRunning = new AtomicBoolean(false);
    private final AtomicBoolean articleSyncRunning = new AtomicBoolean(false);
    private final AtomicBoolean cacheOptimizationRunning = new AtomicBoolean(false);
    private final AtomicBoolean dataCleanupRunning = new AtomicBoolean(false);
    private final AtomicBoolean llmProcessingRunning = new AtomicBoolean(false);
    private final AtomicBoolean rssStage1Running = new AtomicBoolean(false);
    private final AtomicBoolean rssStage2Running = new AtomicBoolean(false);

    // Adaptive scheduling tracking
    private volatile LocalDateTime lastVolatilityCheck = LocalDateTime.now();
    private volatile LocalDateTime lastCryptoSync = LocalDateTime.now().minusMinutes(10); // Allow first sync
    private volatile double currentMarketVolatility = 0.0;
    private volatile long currentSyncInterval = 300000; // Initialize with base interval

    /**
     * Synchronize cryptocurrency data with adaptive scheduling based on market volatility.
     * Base interval: 5 minutes, High volatility: 1 minute, Low volatility: 10 minutes
     */
    @Scheduled(fixedRate = 300000) // Keep base 5-minute schedule, but use adaptive logic internally
    public void synchronizeCryptocurrencyData() {
        if (!cryptoSyncEnabled || cryptoSyncRunning.get()) {
            return;
        }

        // Use adaptive scheduling logic to determine if we should sync now
        if (!shouldSyncBasedOnAdaptiveSchedule()) {
            log.debug("Skipping sync due to adaptive scheduling (current interval: {}ms, volatility: {}%)",
                    currentSyncInterval, String.format("%.2f", currentMarketVolatility));
            return;
        }

        log.info("Starting adaptive cryptocurrency data synchronization (interval: {}ms, volatility: {}%)",
                currentSyncInterval, String.format("%.2f", currentMarketVolatility));

        executeWithDistributedLock("crypto-sync", cryptoSyncRunning, () ->
            cryptoCurrencyUseCase.synchronizeCryptocurrencyData(false)
                    .doOnNext(count -> {
                        cryptoSyncCount.incrementAndGet();
                        log.info("Cryptocurrency sync completed: {} cryptocurrencies updated", count);

                        // Update market volatility after sync
                        updateMarketVolatilityAndSchedule();
                    })
                    .doOnError(error -> log.error("Error during cryptocurrency sync", error))
        );
    }

    /**
     * DISABLED: Stage 1 RSS feed data collection.
     * @deprecated Replaced by ArticleProcessingScheduler
     */
    @Deprecated
    // @Scheduled(fixedRate = 300000) // DISABLED - Replaced by ArticleProcessingScheduler
    public void collectRssFeedData() {
        log.warn("DEPRECATED: collectRssFeedData() called - use ArticleProcessingScheduler instead");
    }

    /**
     * DISABLED: Stage 2 RSS LLM processing.
     * @deprecated Replaced by ArticleProcessingScheduler
     */
    @Deprecated
    // @Scheduled(fixedRate = 600000) // DISABLED - Replaced by ArticleProcessingScheduler
    public void processRssDataWithLlm() {
        log.warn("DEPRECATED: processRssDataWithLlm() called - use ArticleProcessingScheduler instead");
    }

    /**
     * Legacy article synchronization (kept for backward compatibility).
     * @deprecated Use two-stage RSS processing instead
     */
    @Deprecated
    @Scheduled(fixedRate = 1800000) // 30 minutes (reduced frequency)
    public void synchronizeArticlesFromRss() {
        if (!articleSyncEnabled || articleSyncRunning.get()) {
            return;
        }

        log.info("Starting legacy article synchronization from RSS");

        executeWithDistributedLock("article-sync", articleSyncRunning, () ->
            articleUseCase.synchronizeArticlesFromRss(50) // Reduced batch size
                    .doOnNext(count -> {
                        articleSyncCount.incrementAndGet();
                        log.info("Legacy article sync completed: {} articles synchronized", count);
                    })
                    .doOnError(error -> log.error("Error during legacy article sync", error))
        );
    }

    /**
     * DISABLED: Process failed RSS articles.
     * @deprecated Replaced by ArticleProcessingScheduler
     */
    @Deprecated
    // @Scheduled(fixedRate = 1800000) // DISABLED - Replaced by ArticleProcessingScheduler
    public void processFailedRssArticles() {
        log.warn("DEPRECATED: processFailedRssArticles() called - use ArticleProcessingScheduler instead");
    }

    /**
     * DISABLED: Process high priority RSS articles.
     * @deprecated Replaced by ArticleProcessingScheduler
     */
    @Deprecated
    // @Scheduled(fixedRate = 120000) // DISABLED - Replaced by ArticleProcessingScheduler
    public void processHighPriorityRssArticles() {
        log.warn("DEPRECATED: processHighPriorityRssArticles() called - use ArticleProcessingScheduler instead");
    }

    /**
     * Legacy LLM processing (kept for backward compatibility).
     * @deprecated Use two-stage RSS processing instead
     */
    @Deprecated
    @Scheduled(fixedRate = 1800000) // 30 minutes (reduced frequency)
    public void processArticlesWithLlm() {
        if (!llmProcessingEnabled || llmProcessingRunning.get()) {
            return;
        }

        log.info("Starting legacy LLM processing for articles");

        executeWithDistributedLock("llm-processing", llmProcessingRunning, () ->
            articleUseCase.processArticlesNeedingLlmAnalysis(15) // Reduced batch size
                    .doOnNext(count -> {
                        llmProcessingCount.incrementAndGet();
                        log.info("Legacy LLM processing completed: {} articles processed", count);
                    })
                    .doOnError(error -> log.error("Error during legacy LLM processing", error))
        );
    }

    /**
     * Optimize cache performance every 30 minutes.
     */
    @Scheduled(fixedRate = 1800000) // 30 minutes
    public void optimizeCachePerformance() {
        if (!cacheOptimizationEnabled || cacheOptimizationRunning.get()) {
            return;
        }

        log.info("Starting scheduled cache optimization");
        
        executeWithDistributedLock("cache-optimization", cacheOptimizationRunning, () -> 
            cacheUseCase.cleanupExpiredEntries()
                    .map(count -> count.intValue())
                    .doOnNext(count -> {
                        cacheOptimizationCount.incrementAndGet();
                        log.info("Cache optimization completed: {} entries optimized", count);
                    })
                    .doOnError(error -> log.error("Error during cache optimization", error))
        );
    }

    /**
     * Clean up stale data every hour.
     */
    @Scheduled(fixedRate = 3600000) // 1 hour
    public void cleanupStaleData() {
        if (!dataCleanupEnabled || dataCleanupRunning.get()) {
            return;
        }

        log.info("Starting scheduled data cleanup");
        
        executeWithDistributedLock("data-cleanup", dataCleanupRunning, () -> 
            performDataCleanup()
                    .doOnNext(count -> {
                        dataCleanupCount.incrementAndGet();
                        log.info("Data cleanup completed: {} items cleaned", count);
                    })
                    .doOnError(error -> log.error("Error during data cleanup", error))
        );
    }

    /**
     * Refresh featured articles cache every 2 hours.
     */
    @Scheduled(fixedRate = 7200000) // 2 hours
    public void refreshFeaturedArticlesCache() {
        if (!cacheOptimizationEnabled) {
            return;
        }

        log.info("Starting scheduled featured articles cache refresh");
        
        articleUseCase.refreshFeaturedArticlesCache()
                .doOnSuccess(v -> log.info("Featured articles cache refreshed"))
                .doOnError(error -> log.error("Error refreshing featured articles cache", error))
                .subscribe();
    }

    /**
     * Warm cache with popular data every 4 hours.
     */
    @Scheduled(fixedRate = 14400000) // 4 hours
    public void warmCacheWithPopularData() {
        if (!cacheOptimizationEnabled) {
            return;
        }

        log.info("Starting scheduled cache warming");
        
        cacheWarmingService.warmCacheForCategories(List.of("trending", "popular"))
                .doOnNext(count -> log.info("Cache warming completed: {} entries warmed", count))
                .doOnError(error -> log.error("Error during cache warming", error))
                .subscribe();
    }

    /**
     * Health check for background jobs every 5 minutes.
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void performJobHealthCheck() {
        log.debug("Performing background job health check");
        
        getJobStatistics()
                .doOnNext(stats -> {
                    log.debug("Job statistics: {} total executions, {} active jobs", 
                            stats.getTotalExecutions(), stats.getActiveJobs());
                    
                    // Check for stuck jobs (running for more than expected time)
                    if (stats.getActiveJobs() > 0) {
                        log.info("Active background jobs detected: {}", stats.getActiveJobs());
                    }
                })
                .doOnError(error -> log.error("Error during job health check", error))
                .subscribe();
    }

    /**
     * Execute job with simple locking (no distributed locking).
     */
    private void executeWithDistributedLock(String jobName, AtomicBoolean runningFlag,
                                          java.util.function.Supplier<Mono<Integer>> jobExecution) {
        // Execute without distributed locking since it's not available in CacheUseCase
        executeJob(jobName, runningFlag, jobExecution).subscribe();
    }

    /**
     * Execute job with error handling and monitoring.
     */
    private Mono<Integer> executeJob(String jobName, AtomicBoolean runningFlag, 
                                   java.util.function.Supplier<Mono<Integer>> jobExecution) {
        if (runningFlag.compareAndSet(false, true)) {
            long startTime = System.currentTimeMillis();
            
            return jobExecution.get()
                    .doOnNext(result -> {
                        long duration = System.currentTimeMillis() - startTime;
                        log.info("Job {} completed successfully in {}ms: {} items processed", 
                                jobName, duration, result);
                    })
                    .doOnError(error -> {
                        long duration = System.currentTimeMillis() - startTime;
                        log.error("Job {} failed after {}ms", jobName, duration, error);
                    })
                    .doFinally(signalType -> {
                        runningFlag.set(false);
                        log.debug("Job {} finished with signal: {}", jobName, signalType);
                    });
        } else {
            log.debug("Job {} is already running, skipping execution", jobName);
            return Mono.just(0);
        }
    }

    /**
     * Perform comprehensive data cleanup.
     */
    private Mono<Integer> performDataCleanup() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7);
        
        return Mono.zip(
                cryptoCurrencyUseCase.cleanupStaleData(cutoffTime),
                articleUseCase.cleanupOldArticles(90)
        )
        .map(tuple -> tuple.getT1() + tuple.getT2())
        .doOnNext(total -> log.info("Data cleanup removed {} stale entries", total));
    }

    /**
     * Get job execution statistics.
     */
    public Mono<JobStatistics> getJobStatistics() {
        return Mono.fromCallable(() -> {
            long totalExecutions = cryptoSyncCount.get() + articleSyncCount.get() +
                                 cacheOptimizationCount.get() + dataCleanupCount.get() +
                                 llmProcessingCount.get() + rssStage1Count.get() + rssStage2Count.get();

            int activeJobs = (cryptoSyncRunning.get() ? 1 : 0) +
                           (articleSyncRunning.get() ? 1 : 0) +
                           (cacheOptimizationRunning.get() ? 1 : 0) +
                           (dataCleanupRunning.get() ? 1 : 0) +
                           (llmProcessingRunning.get() ? 1 : 0) +
                           (rssStage1Running.get() ? 1 : 0) +
                           (rssStage2Running.get() ? 1 : 0);

            return new JobStatisticsImpl(
                    totalExecutions,
                    activeJobs,
                    cryptoSyncCount.get(),
                    articleSyncCount.get(),
                    cacheOptimizationCount.get(),
                    dataCleanupCount.get(),
                    llmProcessingCount.get(),
                    rssStage1Count.get(),
                    rssStage2Count.get(),
                    LocalDateTime.now(),
                    distributedLockingEnabled
            );
        });
    }

    /**
     * Get job configuration.
     */
    public JobConfiguration getJobConfiguration() {
        return new JobConfigurationImpl(
                cryptoSyncEnabled,
                articleSyncEnabled,
                cacheOptimizationEnabled,
                dataCleanupEnabled,
                llmProcessingEnabled,
                distributedLockingEnabled,
                lockTimeout,
                lockTtl
        );
    }

    /**
     * Manually trigger cryptocurrency sync.
     */
    public Mono<Integer> triggerCryptocurrencySync(boolean forceRefresh) {
        log.info("Manually triggering cryptocurrency sync (force: {})", forceRefresh);
        
        return executeJob("manual-crypto-sync", new AtomicBoolean(false), () -> 
            cryptoCurrencyUseCase.synchronizeCryptocurrencyData(forceRefresh)
        );
    }

    /**
     * Manually trigger article sync.
     */
    public Mono<Integer> triggerArticleSync(int limit) {
        log.info("Manually triggering article sync (limit: {})", limit);
        
        return executeJob("manual-article-sync", new AtomicBoolean(false), () -> 
            articleUseCase.synchronizeArticlesFromRss(limit)
        );
    }

    /**
     * Manually trigger LLM processing.
     */
    public Mono<Integer> triggerLlmProcessing(int batchSize) {
        log.info("Manually triggering LLM processing (batch size: {})", batchSize);

        return executeJob("manual-llm-processing", new AtomicBoolean(false), () ->
            articleUseCase.processArticlesNeedingLlmAnalysis(batchSize)
        );
    }

    /**
     * Manually trigger Stage 1 RSS processing.
     */
    public Mono<Integer> triggerRssStage1Processing(int limit) {
        log.info("Manually triggering Stage 1 RSS processing (limit: {})", limit);

        return executeJob("manual-rss-stage1", new AtomicBoolean(false), () ->
            rssFeedRawProcessingService.processAllRssFeeds(limit)
        );
    }

    /**
     * DEPRECATED: Manually trigger Stage 2 RSS processing.
     * @deprecated Use ArticleProcessingScheduler.triggerStage2Processing() instead
     */
    @Deprecated
    public Mono<Integer> triggerRssStage2Processing(int batchSize) {
        log.warn("DEPRECATED: triggerRssStage2Processing() called - use ArticleProcessingScheduler instead");
        return Mono.just(0);
    }

    /**
     * DEPRECATED: Manually trigger failed RSS articles retry.
     * @deprecated Use ArticleProcessingScheduler instead
     */
    @Deprecated
    public Mono<Integer> triggerFailedRssRetry(int batchSize) {
        log.warn("DEPRECATED: triggerFailedRssRetry() called - use ArticleProcessingScheduler instead");
        return Mono.just(0);
    }

    // DTOs for job statistics and configuration
    public interface JobStatistics {
        Long getTotalExecutions();
        Integer getActiveJobs();
        Long getCryptoSyncCount();
        Long getArticleSyncCount();
        Long getCacheOptimizationCount();
        Long getDataCleanupCount();
        Long getLlmProcessingCount();
        Long getRssStage1Count();
        Long getRssStage2Count();
        LocalDateTime getLastUpdated();
        Boolean isDistributedLockingEnabled();
    }

    public interface JobConfiguration {
        Boolean isCryptoSyncEnabled();
        Boolean isArticleSyncEnabled();
        Boolean isCacheOptimizationEnabled();
        Boolean isDataCleanupEnabled();
        Boolean isLlmProcessingEnabled();
        Boolean isDistributedLockingEnabled();
        Duration getLockTimeout();
        Duration getLockTtl();
    }

    private record JobStatisticsImpl(
            Long totalExecutions, Integer activeJobs, Long cryptoSyncCount,
            Long articleSyncCount, Long cacheOptimizationCount, Long dataCleanupCount,
            Long llmProcessingCount, Long rssStage1Count, Long rssStage2Count,
            LocalDateTime lastUpdated, Boolean isDistributedLockingEnabled
    ) implements JobStatistics {
        @Override
        public Long getTotalExecutions() { return totalExecutions; }
        @Override
        public Integer getActiveJobs() { return activeJobs; }
        @Override
        public Long getCryptoSyncCount() { return cryptoSyncCount; }
        @Override
        public Long getArticleSyncCount() { return articleSyncCount; }
        @Override
        public Long getCacheOptimizationCount() { return cacheOptimizationCount; }
        @Override
        public Long getDataCleanupCount() { return dataCleanupCount; }
        @Override
        public Long getLlmProcessingCount() { return llmProcessingCount; }
        @Override
        public Long getRssStage1Count() { return rssStage1Count; }
        @Override
        public Long getRssStage2Count() { return rssStage2Count; }
        @Override
        public LocalDateTime getLastUpdated() { return lastUpdated; }
        @Override
        public Boolean isDistributedLockingEnabled() { return isDistributedLockingEnabled; }
    }

    private record JobConfigurationImpl(
            Boolean isCryptoSyncEnabled, Boolean isArticleSyncEnabled, Boolean isCacheOptimizationEnabled,
            Boolean isDataCleanupEnabled, Boolean isLlmProcessingEnabled, Boolean isDistributedLockingEnabled,
            Duration lockTimeout, Duration lockTtl
    ) implements JobConfiguration {
        @Override
        public Boolean isCryptoSyncEnabled() { return isCryptoSyncEnabled; }
        @Override
        public Boolean isArticleSyncEnabled() { return isArticleSyncEnabled; }
        @Override
        public Boolean isCacheOptimizationEnabled() { return isCacheOptimizationEnabled; }
        @Override
        public Boolean isDataCleanupEnabled() { return isDataCleanupEnabled; }
        @Override
        public Boolean isLlmProcessingEnabled() { return isLlmProcessingEnabled; }
        @Override
        public Boolean isDistributedLockingEnabled() { return isDistributedLockingEnabled; }
        @Override
        public Duration getLockTimeout() { return lockTimeout; }
        @Override
        public Duration getLockTtl() { return lockTtl; }
    }

    /**
     * Check if it's time to sync based on adaptive scheduling interval.
     */
    private boolean shouldSyncBasedOnAdaptiveSchedule() {
        LocalDateTime now = LocalDateTime.now();
        Duration timeSinceLastSync = Duration.between(lastCryptoSync, now);
        long millisSinceLastSync = timeSinceLastSync.toMillis();

        boolean shouldSync = millisSinceLastSync >= currentSyncInterval;

        if (shouldSync) {
            lastCryptoSync = now; // Update last sync time
            log.debug("Adaptive sync triggered: {}ms since last sync (threshold: {}ms)",
                    millisSinceLastSync, currentSyncInterval);
        }

        return shouldSync;
    }

    /**
     * Update market volatility and adjust sync interval accordingly.
     * This method calculates market volatility based on recent price changes
     * and adjusts the synchronization interval dynamically.
     */
    private void updateMarketVolatilityAndSchedule() {
        try {
            // Calculate market volatility based on recent cryptocurrency data
            cryptoCurrencyUseCase.getMarketStatistics()
                    .subscribe(stats -> {
                        if (stats != null && stats.getAverageChange24h() != null) {
                            double volatility = Math.abs(stats.getAverageChange24h().doubleValue());
                            updateVolatilityAndInterval(volatility);
                        }
                    }, error -> {
                        log.warn("Failed to update market volatility, using default interval", error);
                        currentSyncInterval = baseInterval;
                    });
        } catch (Exception e) {
            log.warn("Error updating market volatility, using default interval", e);
            currentSyncInterval = baseInterval;
        }
    }

    /**
     * Update volatility metrics and adjust sync interval.
     */
    private void updateVolatilityAndInterval(double volatility) {
        currentMarketVolatility = volatility;
        lastVolatilityCheck = LocalDateTime.now();

        // Adjust sync interval based on volatility
        if (volatility > volatilityThreshold) {
            currentSyncInterval = highVolatilityInterval;
            log.info("High market volatility detected ({}%), switching to high-frequency sync ({}ms)",
                    String.format("%.2f", volatility), highVolatilityInterval);
        } else if (volatility < volatilityThreshold / 2) {
            currentSyncInterval = lowVolatilityInterval;
            log.info("Low market volatility detected ({}%), switching to low-frequency sync ({}ms)",
                    String.format("%.2f", volatility), lowVolatilityInterval);
        } else {
            currentSyncInterval = baseInterval;
            log.debug("Normal market volatility ({}%), using base sync interval ({}ms)",
                    String.format("%.2f", volatility), baseInterval);
        }
    }
}
