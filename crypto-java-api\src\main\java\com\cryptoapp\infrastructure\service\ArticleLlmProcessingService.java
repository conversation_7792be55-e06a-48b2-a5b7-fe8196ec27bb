package com.cryptoapp.infrastructure.service;

import com.cryptoapp.domain.ArticleProcessed;
import com.cryptoapp.domain.RssFeedRaw;
import com.cryptoapp.infrastructure.repository.ArticleProcessedRepository;
import com.cryptoapp.infrastructure.repository.RssFeedRawRepository;
import com.cryptoapp.port.in.LlmUseCase;
import io.r2dbc.postgresql.codec.Json;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

/**
 * Clean Stage 2 LLM Article Processor.
 *
 * This service handles the second stage of the two-stage RSS processing pipeline:
 * - Reads pending articles from rss_feed_raw table (status = PENDING)
 * - Processes articles through LLM for content analysis and enhancement
 * - Generates structured content, sentiment analysis, and scoring
 * - Saves processed articles to articles_processed table for frontend consumption
 * - Updates processing status in rss_feed_raw table to PROCESSED
 *
 * Design principles:
 * - Single responsibility: Only handles LLM processing and article enhancement
 * - No RSS fetching (handled by Stage 1)
 * - Comprehensive logging for debugging
 * - Proper error handling and retry logic
 * - Industry-standard reactive patterns
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleLlmProcessingService {

    private final RssFeedRawRepository rssFeedRawRepository;
    private final ArticleProcessedRepository articleProcessedRepository;
    private final LlmUseCase llmUseCase;

    // Processing metrics
    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger failedCount = new AtomicInteger(0);
    private final AtomicInteger skippedCount = new AtomicInteger(0);

    // Slug generation pattern
    private static final Pattern SLUG_PATTERN = Pattern.compile("[^a-z0-9]+");

    // LLM processing timeout (in seconds)
    private static final int LLM_TIMEOUT_SECONDS = 60;

    /**
     * Main entry point: Process pending articles through LLM.
     * This is the primary method called by the Stage 2 scheduler.
     */
    public Mono<Integer> processPendingArticles(int batchSize) {
        log.info("=== Starting Stage 2 LLM Article Processing ===");
        log.info("Processing batch size: {} articles", batchSize);

        resetCounters();
        long startTime = System.currentTimeMillis();

        return rssFeedRawRepository.findPendingForProcessing(batchSize)
                .doOnNext(rawArticle -> log.debug("Found pending article: {} from {}",
                    rawArticle.getTitle(), rawArticle.getSource()))
                .flatMap(this::processArticleThroughLlm)
                .collectList()
                .map(results -> {
                    long processingTime = System.currentTimeMillis() - startTime;
                    int totalProcessed = processedCount.get();
                    int totalFailed = failedCount.get();
                    int totalSkipped = skippedCount.get();

                    log.info("=== Stage 2 LLM Processing Completed ===");
                    log.info("Processing time: {}ms", processingTime);
                    log.info("Articles processed: {}", totalProcessed);
                    log.info("Articles failed: {}", totalFailed);
                    log.info("Articles skipped: {}", totalSkipped);

                    return totalProcessed;
                })
                .doOnError(error -> {
                    log.error("=== Stage 2 LLM Processing Failed ===", error);
                    failedCount.incrementAndGet();
                });
    }

    /**
     * Reset all processing counters for a new processing run.
     */
    private void resetCounters() {
        processedCount.set(0);
        failedCount.set(0);
        skippedCount.set(0);
        log.debug("Processing counters reset");
    }

    /**
     * Process a single article through the complete LLM pipeline.
     */
    private Mono<ArticleProcessed> processArticleThroughLlm(RssFeedRaw rawArticle) {
        log.debug("Processing article through LLM: {} from {}", rawArticle.getTitle(), rawArticle.getSource());

        return Mono.just(rawArticle)
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(this::markAsProcessingStarted)
                .flatMap(this::checkIfAlreadyProcessed)
                .flatMap(this::validateForLlmProcessing)
                .flatMap(this::processWithLlm)
                .flatMap(this::saveProcessedArticle)
                .flatMap(this::markRawAsProcessed)
                .doOnNext(processed -> {
                    processedCount.incrementAndGet();
                    log.debug("Successfully processed article: {} (ID: {})",
                            processed.getTitle(), processed.getId());
                })
                .doOnError(error -> {
                    failedCount.incrementAndGet();
                    log.error("Failed to process article: {} (ID: {})",
                            rawArticle.getTitle(), rawArticle.getId(), error);
                    markRawAsFailed(rawArticle, error.getMessage()).subscribe();
                })
                .onErrorResume(error -> Mono.empty()); // Continue processing other articles
    }

    /**
     * Mark raw article as processing started.
     */
    private Mono<RssFeedRaw> markAsProcessingStarted(RssFeedRaw rawArticle) {
        rawArticle.markProcessingStarted();
        return rssFeedRawRepository.save(rawArticle)
                .doOnNext(saved -> log.debug("Marked article as processing started: {}", saved.getTitle()));
    }

    /**
     * Check if article is already processed to avoid duplicates.
     */
    private Mono<RssFeedRaw> checkIfAlreadyProcessed(RssFeedRaw rawArticle) {
        return articleProcessedRepository.existsByGuid(rawArticle.getGuid())
                .flatMap(exists -> {
                    if (exists) {
                        skippedCount.incrementAndGet();
                        log.debug("Article already processed, skipping: {}", rawArticle.getGuid());
                        rawArticle.markSkipped("Already processed");
                        return rssFeedRawRepository.save(rawArticle).then(Mono.empty());
                    }
                    return Mono.just(rawArticle);
                });
    }

    /**
     * Validate article for LLM processing.
     */
    private Mono<RssFeedRaw> validateForLlmProcessing(RssFeedRaw rawArticle) {
        if (rawArticle.getTitle() == null || rawArticle.getTitle().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Article title is required"));
        }

        if (rawArticle.getGuid() == null || rawArticle.getGuid().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Article GUID is required"));
        }

        if (!rawArticle.canRetryProcessing()) {
            skippedCount.incrementAndGet();
            log.debug("Article cannot be retried, skipping: {}", rawArticle.getGuid());
            rawArticle.markProcessingFailed("Max retry attempts reached");
            return rssFeedRawRepository.save(rawArticle).then(Mono.empty());
        }

        return Mono.just(rawArticle);
    }

    /**
     * Process article content through LLM and create processed article.
     */
    private Mono<ArticleProcessed> processWithLlm(RssFeedRaw rawArticle) {
        log.debug("Processing article with LLM: {}", rawArticle.getTitle());
        long startTime = System.currentTimeMillis();

        // Create a temporary Article object for LLM processing
        com.cryptoapp.domain.Article tempArticle = com.cryptoapp.domain.Article.builder()
                .title(rawArticle.getTitle())
                .summary(rawArticle.getDescription())
                .body(rawArticle.getContent() != null ?
                      Json.of("{\"content\":\"" + rawArticle.getContent().replace("\"", "\\\"") + "\"}") : null)
                .source(rawArticle.getSource())
                .sourceUrl(rawArticle.getSourceUrl())
                .publishedDate(rawArticle.getPublishedDate())
                .guid(rawArticle.getGuid())
                .build();

        return llmUseCase.processArticleWithLlm(tempArticle)
                .timeout(java.time.Duration.ofSeconds(LLM_TIMEOUT_SECONDS))
                .map(processedArticle -> {
                    long processingTime = System.currentTimeMillis() - startTime;
                    log.debug("LLM processing completed in {}ms for article: {}", processingTime, rawArticle.getTitle());

                    return createProcessedArticleFromLlmArticle(rawArticle, processedArticle, processingTime);
                })
                .doOnError(error -> log.error("LLM processing failed for article: {} - {}",
                        rawArticle.getTitle(), error.getMessage()));
    }



    /**
     * Create ArticleProcessed from LLM-processed Article.
     */
    private ArticleProcessed createProcessedArticleFromLlmArticle(RssFeedRaw rawArticle,
                                                                 com.cryptoapp.domain.Article processedArticle,
                                                                 long processingTime) {
        return ArticleProcessed.builder()
                .rssFeedRawId(rawArticle.getId())
                .guid(rawArticle.getGuid())
                .slug(generateSlug(rawArticle.getTitle()))
                .title(rawArticle.getTitle())
                .summary(rawArticle.getDescription())
                .category(rawArticle.getCategory())
                .source(rawArticle.getSource())
                .sourceUrl(rawArticle.getSourceUrl())
                .imageUrl(rawArticle.getImageUrl())
                .author(rawArticle.getAuthor())
                .publishedDate(rawArticle.getPublishedDate())
                .headline(processedArticle.getHeadline() != null ? processedArticle.getHeadline() : rawArticle.getTitle())
                .sections(processedArticle.getSections())
                .tags(processedArticle.getTags())
                .highlightedCoins(processedArticle.getHighlightedCoins())
                .tone(processedArticle.getTone())
                .sentiment(processedArticle.getSentiment() != null ? processedArticle.getSentiment().toString().toLowerCase() : "neutral")
                .hotScore(processedArticle.getHotScore() != null ? processedArticle.getHotScore() : 50)
                .relevanceScore(processedArticle.getHotScore() != null ? BigDecimal.valueOf(processedArticle.getHotScore() / 100.0) : BigDecimal.valueOf(0.5))
                .qualityScore(processedArticle.getHotScore() != null ? BigDecimal.valueOf(Math.min(processedArticle.getHotScore() + 10, 100) / 100.0) : BigDecimal.valueOf(0.5))
                .summaries(processedArticle.getSummaries())
                .body(processedArticle.getBody())
                .llmModelUsed("local-lm-studio")
                .llmProcessingTimeMs((int) processingTime)
                .rawLlmResponse(processedArticle.getRawLlmResponse())
                .estimatedReadTime(calculateReadTime(rawArticle))
                .wordCount(calculateWordCount(rawArticle))
                .build();
    }

    /**
     * Save processed article to database.
     */
    private Mono<ArticleProcessed> saveProcessedArticle(ArticleProcessed processed) {
        return articleProcessedRepository.save(processed)
                .doOnNext(saved -> log.debug("Saved processed article: {} (ID: {})",
                        saved.getTitle(), saved.getId()))
                .doOnError(error -> log.error("Error saving processed article: {}",
                        processed.getTitle(), error));
    }

    /**
     * Mark raw article as successfully processed.
     */
    private Mono<ArticleProcessed> markRawAsProcessed(ArticleProcessed processed) {
        return rssFeedRawRepository.findById(processed.getRssFeedRawId())
                .flatMap(rawArticle -> {
                    rawArticle.markProcessingCompleted();
                    return rssFeedRawRepository.save(rawArticle);
                })
                .then(Mono.just(processed))
                .doOnNext(article -> log.debug("Marked raw article as processed: {}", article.getGuid()));
    }

    /**
     * Mark raw article as failed.
     */
    private Mono<Integer> markRawAsFailed(RssFeedRaw rawArticle, String error) {
        rawArticle.markProcessingFailed(error);
        return rssFeedRawRepository.save(rawArticle)
                .map(saved -> 1)
                .doOnNext(result -> log.debug("Marked raw article as failed: {} - {}",
                        rawArticle.getGuid(), error));
    }


    /**
     * Generate URL-friendly slug from title.
     */
    private String generateSlug(String title) {
        if (title == null || title.trim().isEmpty()) {
            return "article-" + System.currentTimeMillis();
        }

        return SLUG_PATTERN.matcher(title.toLowerCase().trim())
                .replaceAll("-")
                .replaceAll("^-+|-+$", "")
                .substring(0, Math.min(title.length(), 100));
    }

    /**
     * Extract string value from LLM JSON response.
     */
    private String extractFromLlm(JsonNode llmJson, String field, String defaultValue) {
        JsonNode fieldNode = llmJson.get(field);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asText() : defaultValue;
    }

    /**
     * Extract integer value from LLM JSON response.
     */
    private Integer extractIntFromLlm(JsonNode llmJson, String field, Integer defaultValue) {
        JsonNode fieldNode = llmJson.get(field);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asInt() : defaultValue;
    }

    /**
     * Extract double value from LLM JSON response.
     */
    private Double extractDoubleFromLlm(JsonNode llmJson, String field, Double defaultValue) {
        JsonNode fieldNode = llmJson.get(field);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asDouble() : defaultValue;
    }

    /**
     * Create basic processed article when LLM processing fails.
     */
    private ArticleProcessed createBasicProcessedArticle(RssFeedRaw rawArticle,
                                                        String llmResponse,
                                                        long processingTime) {
        return ArticleProcessed.builder()
                .rssFeedRawId(rawArticle.getId())
                .guid(rawArticle.getGuid())
                .slug(generateSlug(rawArticle.getTitle()))
                .title(rawArticle.getTitle())
                .summary(rawArticle.getDescription())
                .category(rawArticle.getCategory())
                .source(rawArticle.getSource())
                .sourceUrl(rawArticle.getSourceUrl())
                .imageUrl(rawArticle.getImageUrl())
                .author(rawArticle.getAuthor())
                .publishedDate(rawArticle.getPublishedDate())
                .headline(rawArticle.getTitle())
                .tone("neutral")
                .sentiment("neutral")
                .hotScore(50)
                .relevanceScore(BigDecimal.valueOf(0.5))
                .qualityScore(BigDecimal.valueOf(0.5))
                .llmModelUsed("local-lm-studio")
                .llmProcessingTimeMs((int) processingTime)
                .rawLlmResponse(llmResponse)
                .estimatedReadTime(calculateReadTime(rawArticle))
                .wordCount(calculateWordCount(rawArticle))
                .build();
    }

    /**
     * Calculate word count from raw article.
     */
    private Integer calculateWordCount(RssFeedRaw rawArticle) {
        int count = 0;

        if (rawArticle.getTitle() != null && !rawArticle.getTitle().trim().isEmpty()) {
            count += rawArticle.getTitle().trim().split("\\s+").length;
        }

        if (rawArticle.getDescription() != null && !rawArticle.getDescription().trim().isEmpty()) {
            count += rawArticle.getDescription().trim().split("\\s+").length;
        }

        if (rawArticle.getContent() != null && !rawArticle.getContent().trim().isEmpty()) {
            count += rawArticle.getContent().trim().split("\\s+").length;
        }

        return count;
    }

    /**
     * Calculate estimated read time based on word count.
     */
    private String calculateReadTime(RssFeedRaw rawArticle) {
        int wordCount = calculateWordCount(rawArticle);
        int readTimeMinutes = Math.max(1, wordCount / 200); // Average reading speed: 200 words/minute
        return readTimeMinutes + " min read";
    }

    /**
     * Get processing statistics for monitoring.
     */
    public ProcessingStatistics getProcessingStatistics() {
        return new ProcessingStatistics(
                processedCount.get(),
                failedCount.get(),
                skippedCount.get()
        );
    }

    /**
     * Processing statistics record for monitoring and metrics.
     */
    public record ProcessingStatistics(int processed, int failed, int skipped) {}
}
