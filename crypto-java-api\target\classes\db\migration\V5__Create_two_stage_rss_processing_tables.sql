-- V5__Create_two_stage_rss_processing_tables.sql
-- Migration to create optimized tables for two-stage RSS feed processing
-- Stage 1: Raw RSS data collection and normalization
-- Stage 2: LLM processing and frontend-ready data preparation

-- =====================================================
-- STAGE 1 TABLE: Raw RSS Feed Data Storage
-- =====================================================

-- Table for storing raw, normalized RSS feed data before LLM processing
CREATE TABLE IF NOT EXISTS rss_feed_raw (
    id BIGSERIAL PRIMARY KEY,
    
    -- RSS Feed Identification
    guid VARCHAR(500) NOT NULL UNIQUE, -- RSS GUID for duplicate prevention
    source VARCHAR(255) NOT NULL, -- RSS feed source name
    source_url TEXT NOT NULL, -- Original article URL
    
    -- Raw Article Content
    title VARCHAR(1000) NOT NULL,
    description TEXT, -- Raw RSS description/summary
    content TEXT, -- Full article content if available
    author VARCHAR(255),
    category VARCHAR(100),
    
    -- Media and Links
    image_url TEXT,
    link TEXT, -- Canonical article link
    
    -- Timestamps
    published_date TIMESTAMP WITH TIME ZONE,
    fetched_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Processing Status
    processing_status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, PROCESSING, PROCESSED, FAILED
    processing_attempts INTEGER NOT NULL DEFAULT 0,
    last_processing_attempt TIMESTAMP WITH TIME ZONE,
    processing_error TEXT,
    
    -- Raw Data Storage (for debugging and reprocessing)
    raw_rss_data JSONB, -- Original RSS entry data
    normalized_data JSONB, -- Cleaned and normalized data
    
    -- Metadata
    feed_priority INTEGER DEFAULT 5, -- Feed priority for processing order
    content_hash VARCHAR(64), -- SHA-256 hash for content deduplication
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- STAGE 2 TABLE: LLM Processed Articles (Frontend Ready)
-- =====================================================

-- Table for storing LLM-processed articles ready for frontend consumption
CREATE TABLE IF NOT EXISTS articles_processed (
    id BIGSERIAL PRIMARY KEY,
    
    -- Reference to raw data
    rss_feed_raw_id BIGINT NOT NULL REFERENCES rss_feed_raw(id) ON DELETE CASCADE,
    
    -- Article Identification
    guid VARCHAR(500) NOT NULL UNIQUE, -- Same as raw table for consistency
    slug VARCHAR(500) NOT NULL UNIQUE, -- SEO-friendly URL slug
    
    -- Basic Article Information
    title VARCHAR(1000) NOT NULL,
    summary TEXT, -- Original summary from RSS
    category VARCHAR(100),
    source VARCHAR(255) NOT NULL,
    source_url TEXT NOT NULL,
    image_url TEXT,
    author VARCHAR(255),
    published_date TIMESTAMP WITH TIME ZONE,
    
    -- LLM-Generated Content Structure
    headline VARCHAR(500), -- AI-generated headline
    sections JSONB, -- Dynamic LLM-generated sections
    key_takeaways JSONB, -- Important points extracted by LLM
    related_links JSONB, -- Related links found by LLM
    
    -- LLM Analysis Results
    sentiment VARCHAR(50), -- bullish, bearish, neutral
    tone VARCHAR(100), -- professional, casual, urgent, etc.
    highlighted_coins JSONB, -- Cryptocurrency symbols mentioned
    tags JSONB, -- AI-generated tags
    
    -- Scoring and Ranking
    hot_score INTEGER CHECK (hot_score >= 0 AND hot_score <= 100),
    relevance_score DECIMAL(5,2) CHECK (relevance_score >= 0 AND relevance_score <= 100),
    quality_score DECIMAL(5,2) CHECK (quality_score >= 0 AND quality_score <= 100),
    
    -- Content Organization
    summaries JSONB, -- Multiple summary types (short, medium, long)
    body JSONB, -- Structured article body
    
    -- LLM Processing Metadata
    llm_model_used VARCHAR(100),
    llm_processing_time_ms INTEGER,
    raw_llm_response TEXT, -- Full LLM response for debugging
    llm_processing_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Content Metrics
    estimated_read_time VARCHAR(50),
    word_count INTEGER,
    
    -- Publication and Caching
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    cache_key VARCHAR(255), -- For caching optimization
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Indexes for rss_feed_raw table
CREATE INDEX IF NOT EXISTS idx_rss_feed_raw_guid ON rss_feed_raw(guid);
CREATE INDEX IF NOT EXISTS idx_rss_feed_raw_source ON rss_feed_raw(source);
CREATE INDEX IF NOT EXISTS idx_rss_feed_raw_processing_status ON rss_feed_raw(processing_status);
CREATE INDEX IF NOT EXISTS idx_rss_feed_raw_fetched_at ON rss_feed_raw(fetched_at DESC);
CREATE INDEX IF NOT EXISTS idx_rss_feed_raw_published_date ON rss_feed_raw(published_date DESC);
CREATE INDEX IF NOT EXISTS idx_rss_feed_raw_content_hash ON rss_feed_raw(content_hash);
CREATE INDEX IF NOT EXISTS idx_rss_feed_raw_priority_status ON rss_feed_raw(feed_priority DESC, processing_status);

-- Composite index for processing queue optimization
CREATE INDEX IF NOT EXISTS idx_rss_feed_raw_processing_queue
ON rss_feed_raw(processing_status, feed_priority DESC, fetched_at ASC)
WHERE processing_status IN ('PENDING', 'FAILED');

-- Indexes for articles_processed table
CREATE INDEX IF NOT EXISTS idx_articles_processed_guid ON articles_processed(guid);
CREATE INDEX IF NOT EXISTS idx_articles_processed_slug ON articles_processed(slug);
CREATE INDEX IF NOT EXISTS idx_articles_processed_source ON articles_processed(source);
CREATE INDEX IF NOT EXISTS idx_articles_processed_category ON articles_processed(category);
CREATE INDEX IF NOT EXISTS idx_articles_processed_published_date ON articles_processed(published_date DESC);
CREATE INDEX IF NOT EXISTS idx_articles_processed_sentiment ON articles_processed(sentiment);
CREATE INDEX IF NOT EXISTS idx_articles_processed_hot_score ON articles_processed(hot_score DESC);
CREATE INDEX IF NOT EXISTS idx_articles_processed_created_at ON articles_processed(created_at DESC);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_articles_processed_featured_trending
ON articles_processed(is_featured, is_trending, hot_score DESC, published_date DESC);

CREATE INDEX IF NOT EXISTS idx_articles_processed_category_score
ON articles_processed(category, hot_score DESC, published_date DESC);

-- JSONB indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_articles_processed_highlighted_coins_gin
ON articles_processed USING GIN (highlighted_coins);

CREATE INDEX IF NOT EXISTS idx_articles_processed_tags_gin
ON articles_processed USING GIN (tags);

CREATE INDEX IF NOT EXISTS idx_rss_feed_raw_normalized_data_gin
ON rss_feed_raw USING GIN (normalized_data);

-- =====================================================
-- CONSTRAINTS AND TRIGGERS
-- =====================================================

-- Add constraint to ensure processing status is valid
ALTER TABLE rss_feed_raw ADD CONSTRAINT chk_rss_feed_raw_processing_status
CHECK (processing_status IN ('PENDING', 'PROCESSING', 'PROCESSED', 'FAILED', 'SKIPPED'));

-- Add constraint to ensure sentiment is valid
ALTER TABLE articles_processed ADD CONSTRAINT chk_articles_processed_sentiment
CHECK (sentiment IN ('bullish', 'bearish', 'neutral', 'mixed'));

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_rss_feed_raw_updated_at
    BEFORE UPDATE ON rss_feed_raw
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_articles_processed_updated_at
    BEFORE UPDATE ON articles_processed
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE rss_feed_raw IS 'Stage 1: Raw RSS feed data storage and normalization before LLM processing';
COMMENT ON TABLE articles_processed IS 'Stage 2: LLM-processed articles ready for frontend consumption';

COMMENT ON COLUMN rss_feed_raw.guid IS 'Unique RSS GUID for duplicate prevention';
COMMENT ON COLUMN rss_feed_raw.processing_status IS 'Current processing status: PENDING, PROCESSING, PROCESSED, FAILED, SKIPPED';
COMMENT ON COLUMN rss_feed_raw.content_hash IS 'SHA-256 hash for content deduplication';
COMMENT ON COLUMN rss_feed_raw.feed_priority IS 'Processing priority (1=highest, 10=lowest)';

COMMENT ON COLUMN articles_processed.rss_feed_raw_id IS 'Reference to original raw RSS data';
COMMENT ON COLUMN articles_processed.sections IS 'Dynamic LLM-generated article sections';
COMMENT ON COLUMN articles_processed.hot_score IS 'Trending score from 0-100 based on various factors';
COMMENT ON COLUMN articles_processed.highlighted_coins IS 'JSON array of cryptocurrency symbols mentioned';
COMMENT ON COLUMN articles_processed.llm_model_used IS 'LLM model identifier used for processing';
