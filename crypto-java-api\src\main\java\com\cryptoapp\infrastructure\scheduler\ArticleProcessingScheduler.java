package com.cryptoapp.infrastructure.scheduler;

import com.cryptoapp.infrastructure.service.ArticleLlmProcessingService;
import com.cryptoapp.infrastructure.service.RssFeedRawProcessingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Clean Article Processing Scheduler.
 * 
 * This scheduler implements the two-stage RSS processing pipeline with clean separation:
 * - Stage 1: Raw RSS data collection and normalization (every 5 minutes)
 * - Stage 2: LLM processing of raw data (every 10 minutes)
 * 
 * Design principles:
 * - Clean separation of concerns
 * - Configurable enable/disable via application.yaml
 * - Comprehensive logging for debugging
 * - Proper error handling and recovery
 * - Industry-standard scheduling patterns
 * - Startup processing for immediate data availability
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleProcessingScheduler {

    private final RssFeedRawProcessingService stage1Service;
    private final ArticleLlmProcessingService stage2Service;

    // Configuration flags
    @Value("${jobs.rss-stage1.enabled:true}")
    private boolean stage1Enabled;

    @Value("${jobs.rss-stage2.enabled:true}")
    private boolean stage2Enabled;

    @Value("${jobs.rss-stage1.batch-size:150}")
    private int stage1BatchSize;

    @Value("${jobs.rss-stage2.batch-size:50}")
    private int stage2BatchSize;

    @Value("${jobs.startup-processing.enabled:true}")
    private boolean startupProcessingEnabled;

    // Processing state tracking
    private final AtomicBoolean stage1Running = new AtomicBoolean(false);
    private final AtomicBoolean stage2Running = new AtomicBoolean(false);

    /**
     * Run initial processing on application startup.
     * This ensures we have fresh data immediately when the application starts.
     */
    @EventListener(ApplicationReadyEvent.class)
    public void runStartupProcessing() {
        if (!startupProcessingEnabled) {
            log.info("Startup processing is disabled");
            return;
        }

        log.info("=== Starting Article Processing on Application Startup ===");
        
        // Run Stage 1 first, then Stage 2
        runStage1Processing()
                .then(Mono.delay(java.time.Duration.ofSeconds(30))) // Wait 30 seconds between stages
                .then(runStage2Processing())
                .subscribe(
                    result -> log.info("=== Startup Article Processing Completed Successfully ==="),
                    error -> log.error("=== Startup Article Processing Failed ===", error)
                );
    }

    /**
     * Stage 1: RSS Raw Data Collection (every 5 minutes).
     * Fetches RSS data from all sources, normalizes it, and saves to rss_feed_raw table.
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void scheduleStage1Processing() {
        if (!stage1Enabled) {
            return;
        }

        if (stage1Running.get()) {
            log.debug("Stage 1 processing already running, skipping this cycle");
            return;
        }

        runStage1Processing().subscribe();
    }

    /**
     * Stage 2: LLM Article Processing (every 10 minutes).
     * Processes pending articles from rss_feed_raw through LLM and saves to articles_processed.
     */
    @Scheduled(fixedRate = 600000) // 10 minutes
    public void scheduleStage2Processing() {
        if (!stage2Enabled) {
            return;
        }

        if (stage2Running.get()) {
            log.debug("Stage 2 processing already running, skipping this cycle");
            return;
        }

        runStage2Processing().subscribe();
    }

    /**
     * Execute Stage 1 processing with proper error handling.
     */
    private Mono<Integer> runStage1Processing() {
        if (!stage1Running.compareAndSet(false, true)) {
            return Mono.just(0);
        }

        log.info("Starting Stage 1 RSS Raw Data Processing");
        long startTime = System.currentTimeMillis();

        return stage1Service.processAllRssFeeds(stage1BatchSize)
                .doOnNext(processed -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("Stage 1 completed: {} articles processed in {}ms", processed, duration);
                })
                .doOnError(error -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.error("Stage 1 failed after {}ms", duration, error);
                })
                .doFinally(signal -> {
                    stage1Running.set(false);
                    log.debug("Stage 1 processing lock released");
                });
    }

    /**
     * Execute Stage 2 processing with proper error handling.
     */
    private Mono<Integer> runStage2Processing() {
        if (!stage2Running.compareAndSet(false, true)) {
            return Mono.just(0);
        }

        log.info("Starting Stage 2 LLM Article Processing");
        long startTime = System.currentTimeMillis();

        return stage2Service.processPendingArticles(stage2BatchSize)
                .doOnNext(processed -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("Stage 2 completed: {} articles processed in {}ms", processed, duration);
                })
                .doOnError(error -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.error("Stage 2 failed after {}ms", duration, error);
                })
                .doFinally(signal -> {
                    stage2Running.set(false);
                    log.debug("Stage 2 processing lock released");
                });
    }

    /**
     * Get current processing status for monitoring.
     */
    public ProcessingStatus getProcessingStatus() {
        return new ProcessingStatus(
                stage1Enabled,
                stage2Enabled,
                stage1Running.get(),
                stage2Running.get(),
                stage1BatchSize,
                stage2BatchSize
        );
    }

    /**
     * Manually trigger Stage 1 processing (for testing/admin purposes).
     */
    public Mono<Integer> triggerStage1Processing() {
        log.info("Manually triggering Stage 1 processing");
        return runStage1Processing();
    }

    /**
     * Manually trigger Stage 2 processing (for testing/admin purposes).
     */
    public Mono<Integer> triggerStage2Processing() {
        log.info("Manually triggering Stage 2 processing");
        return runStage2Processing();
    }

    /**
     * Processing status record for monitoring.
     */
    public record ProcessingStatus(
            boolean stage1Enabled,
            boolean stage2Enabled,
            boolean stage1Running,
            boolean stage2Running,
            int stage1BatchSize,
            int stage2BatchSize
    ) {}
}
