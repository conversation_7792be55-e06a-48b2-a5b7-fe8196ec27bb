-- H2 Database Schema for Crypto Java API
-- This schema creates the necessary tables for development with H2

-- Drop tables if they exist (for clean restart)
DROP TABLE IF EXISTS article_tags;
DROP TABLE IF EXISTS article_highlighted_coins;
DROP TABLE IF EXISTS article_summaries;
DROP TABLE IF EXISTS articles;
DROP TABLE IF EXISTS cryptocurrencies;

-- Cryptocurrencies table
CREATE TABLE cryptocurrencies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    external_id VARCHAR(255) UNIQUE,
    symbol VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(20, 8),
    market_cap DECIMAL(30, 2),
    volume_24h DECIMAL(30, 2),
    change_24h DECIMAL(10, 4),
    change_7d DECIMAL(10, 4),
    change_30d DECIMAL(10, 4),
    rank_position INTEGER,
    market_cap_tier VARCHAR(50),
    last_updated TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Articles table
CREATE TABLE articles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(1000) NOT NULL,
    slug VARCHAR(1000) UNIQUE,
    summary TEXT,
    content TEXT,
    url VARCHAR(2000),
    image_url VARCHAR(2000),
    source VARCHAR(255),
    author VARCHAR(255),
    category VARCHAR(100),
    sentiment VARCHAR(50),
    tone VARCHAR(100),
    hot_score INTEGER DEFAULT 0,
    published_at TIMESTAMP,
    processed_with_llm BOOLEAN DEFAULT FALSE,
    headline VARCHAR(1000),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Article tags (many-to-many relationship)
CREATE TABLE article_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    article_id BIGINT NOT NULL,
    tag VARCHAR(255) NOT NULL,
    FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
    UNIQUE(article_id, tag)
);

-- Article highlighted coins (many-to-many relationship)
CREATE TABLE article_highlighted_coins (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    article_id BIGINT NOT NULL,
    coin_symbol VARCHAR(50) NOT NULL,
    FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
    UNIQUE(article_id, coin_symbol)
);

-- Article summaries (one-to-many relationship)
CREATE TABLE article_summaries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    article_id BIGINT NOT NULL,
    summary_type VARCHAR(50) NOT NULL,
    summary_text TEXT NOT NULL,
    FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
    UNIQUE(article_id, summary_type)
);

-- Create indexes for better performance
CREATE INDEX idx_cryptocurrencies_symbol ON cryptocurrencies(symbol);
CREATE INDEX idx_cryptocurrencies_rank ON cryptocurrencies(rank_position);
CREATE INDEX idx_cryptocurrencies_market_cap ON cryptocurrencies(market_cap);
CREATE INDEX idx_cryptocurrencies_updated ON cryptocurrencies(last_updated);

CREATE INDEX idx_articles_slug ON articles(slug);
CREATE INDEX idx_articles_source ON articles(source);
CREATE INDEX idx_articles_category ON articles(category);
CREATE INDEX idx_articles_sentiment ON articles(sentiment);
CREATE INDEX idx_articles_hot_score ON articles(hot_score);
CREATE INDEX idx_articles_published ON articles(published_at);
CREATE INDEX idx_articles_created ON articles(created_at);
CREATE INDEX idx_articles_llm_processed ON articles(processed_with_llm);

CREATE INDEX idx_article_tags_tag ON article_tags(tag);
CREATE INDEX idx_article_highlighted_coins_symbol ON article_highlighted_coins(coin_symbol);

-- No sample cryptocurrency data - will be fetched from Kraken API

INSERT INTO articles (title, slug, summary, content, url, source, author, category, sentiment, hot_score, published_at, processed_with_llm) VALUES
('Bitcoin Reaches New All-Time High', 'bitcoin-reaches-new-all-time-high', 'Bitcoin surpasses previous records amid institutional adoption.', 'Bitcoin has reached a new all-time high of $45,000 as institutional investors continue to show strong interest in the cryptocurrency market. This milestone comes after months of steady growth and increased adoption by major corporations.', 'https://example.com/bitcoin-ath', 'CoinTelegraph', 'John Crypto', 'MARKET', 'BULLISH', 85, CURRENT_TIMESTAMP - INTERVAL '2' HOUR, true),
('Ethereum 2.0 Staking Reaches Milestone', 'ethereum-2-staking-milestone', 'Ethereum staking participation hits record levels.', 'The Ethereum 2.0 staking mechanism has reached a significant milestone with over 15 million ETH now staked on the network. This represents strong confidence in the networks transition to proof-of-stake.', 'https://example.com/eth-staking', 'Decrypt', 'Jane Blockchain', 'TECHNOLOGY', 'BULLISH', 78, CURRENT_TIMESTAMP - INTERVAL '4' HOUR, true),
('DeFi Protocol Suffers Major Exploit', 'defi-protocol-major-exploit', 'A popular DeFi protocol loses millions in a sophisticated attack.', 'A major DeFi protocol has suffered a significant exploit resulting in the loss of over $50 million in user funds. The attack exploited a vulnerability in the protocols smart contract code.', 'https://example.com/defi-exploit', 'CoinDesk', 'Security Expert', 'DEFI', 'BEARISH', 92, CURRENT_TIMESTAMP - INTERVAL '1' HOUR, true),
('Regulatory Clarity Boosts Market Sentiment', 'regulatory-clarity-boosts-sentiment', 'New regulatory guidelines provide much-needed clarity for crypto markets.', 'Financial regulators have released comprehensive guidelines for cryptocurrency operations, providing the clarity that the market has been seeking. This development is expected to boost institutional adoption.', 'https://example.com/regulation-clarity', 'CoinTelegraph', 'Regulatory Reporter', 'REGULATION', 'BULLISH', 75, CURRENT_TIMESTAMP - INTERVAL '6' HOUR, true),
('NFT Market Shows Signs of Recovery', 'nft-market-recovery-signs', 'NFT trading volumes increase after months of decline.', 'The NFT market is showing signs of recovery with trading volumes increasing by 40% over the past week. New utility-focused projects are driving renewed interest in the space.', 'https://example.com/nft-recovery', 'Decrypt', 'NFT Analyst', 'NFT', 'NEUTRAL', 65, CURRENT_TIMESTAMP - INTERVAL '8' HOUR, true);

-- Insert sample tags
INSERT INTO article_tags (article_id, tag) VALUES
(1, 'Bitcoin'), (1, 'ATH'), (1, 'Institutional'),
(2, 'Ethereum'), (2, 'Staking'), (2, 'ETH2'),
(3, 'DeFi'), (3, 'Security'), (3, 'Exploit'),
(4, 'Regulation'), (4, 'Compliance'), (4, 'Institutional'),
(5, 'NFT'), (5, 'Recovery'), (5, 'Trading');

-- Insert sample highlighted coins
INSERT INTO article_highlighted_coins (article_id, coin_symbol) VALUES
(1, 'BTC'),
(2, 'ETH'),
(3, 'ETH'), (3, 'UNI'), (3, 'AAVE'),
(4, 'BTC'), (4, 'ETH'),
(5, 'ETH');

-- Insert sample summaries
INSERT INTO article_summaries (article_id, summary_type, summary_text) VALUES
(1, 'SHORT', 'Bitcoin hits new ATH of $45,000 with institutional support.'),
(1, 'MEDIUM', 'Bitcoin reaches a new all-time high of $45,000 driven by continued institutional adoption and growing corporate interest in cryptocurrency investments.'),
(1, 'LONG', 'Bitcoin has achieved a significant milestone by reaching a new all-time high of $45,000, marking a historic moment for the cryptocurrency market. This achievement comes as a result of sustained institutional adoption, with major corporations and investment firms increasingly adding Bitcoin to their portfolios as a hedge against inflation and currency devaluation.'),
(2, 'SHORT', 'Ethereum 2.0 staking surpasses 15 million ETH milestone.'),
(2, 'MEDIUM', 'The Ethereum 2.0 network has reached a major milestone with over 15 million ETH now staked, demonstrating strong community confidence in the proof-of-stake transition.'),
(3, 'SHORT', 'DeFi protocol loses $50M in smart contract exploit.'),
(3, 'MEDIUM', 'A major DeFi protocol has suffered a significant security breach resulting in the loss of over $50 million in user funds due to a smart contract vulnerability.');

-- Update article timestamps
UPDATE articles SET updated_at = CURRENT_TIMESTAMP;
