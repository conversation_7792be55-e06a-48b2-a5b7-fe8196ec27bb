-- Fix rank column name to match entity mapping
-- The entity maps rank field to rank_position column

-- First, check if the table exists and create it if it doesn't (in case V1 was baselined)
CREATE TABLE IF NOT EXISTS cryptocurrencies (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    symbol VARCHAR(50) NOT NULL UNIQUE,
    price DECIMAL(20, 8),
    change_24h DECIMAL(10, 4),
    market_cap DECIMAL(30, 2),
    volume_24h DECIMAL(30, 2),
    color VARCHAR(50),
    bg_color VARCHAR(50),
    border_color VARCHAR(50),
    image_url TEXT,

    -- Additional fields from external APIs
    id_str VARCHAR(255),
    rank_position INTEGER,
    price_btc DECIMAL(20, 8),
    available_supply DECIMAL(30, 2),
    total_supply DECIMAL(30, 2),
    max_supply DECIMAL(30, 2),
    percent_change_1h DECIMAL(10, 4),
    percent_change_7d DECIMAL(10, 4),
    last_updated_timestamp BIGINT,

    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_crypto_name ON cryptocurrencies(name);
CREATE INDEX IF NOT EXISTS idx_crypto_symbol ON cryptocurrencies(symbol);
CREATE INDEX IF NOT EXISTS idx_crypto_market_cap_desc ON cryptocurrencies(market_cap DESC);
CREATE INDEX IF NOT EXISTS idx_crypto_price_change ON cryptocurrencies(price, change_24h);
CREATE INDEX IF NOT EXISTS idx_crypto_last_updated ON cryptocurrencies(last_updated DESC);
CREATE INDEX IF NOT EXISTS idx_crypto_volume_desc ON cryptocurrencies(volume_24h DESC);
CREATE INDEX IF NOT EXISTS idx_crypto_rank ON cryptocurrencies(rank_position);

-- If the table already exists with 'rank' column, rename it to 'rank_position'
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'cryptocurrencies'
        AND column_name = 'rank'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE cryptocurrencies RENAME COLUMN rank TO rank_position;
        DROP INDEX IF EXISTS idx_crypto_rank;
        CREATE INDEX IF NOT EXISTS idx_crypto_rank ON cryptocurrencies(rank_position);
    END IF;
END $$;
