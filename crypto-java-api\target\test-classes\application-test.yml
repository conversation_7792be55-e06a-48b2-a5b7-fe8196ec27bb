# Test Profile Configuration
spring:
  # Use H2 in-memory database for tests
  r2dbc:
    url: r2dbc:h2:mem:///testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: ""
  
  # Disable Flyway for tests (use schema.sql instead)
  flyway:
    enabled: false
  
  # Use embedded Redis for tests
  data:
    redis:
      url: redis://localhost:6370  # Different port for test Redis
      ssl:
        enabled: false

# Logging for tests
logging:
  level:
    com.cryptoapp: DEBUG
    org.springframework.test: DEBUG
    org.testcontainers: INFO
  pattern:
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n"

# Disable scheduling for tests
scheduling:
  enabled: false

# Fast cache settings for tests
performance:
  max-concurrent-tasks: 2
  cache:
    ttl:
      market-data: 5     # 5 seconds for fast tests
      articles: 60       # 1 minute
      ohlc: 10          # 10 seconds

# Lenient circuit breaker for tests
resilience4j:
  circuitbreaker:
    instances:
      llm-provider:
        failure-rate-threshold: 90
        wait-duration-in-open-state: 1s
        sliding-window-size: 5
        minimum-number-of-calls: 2
      external-api:
        failure-rate-threshold: 90
        wait-duration-in-open-state: 1s
        sliding-window-size: 5
        minimum-number-of-calls: 2

# Test LLM configuration (mock providers)
llm:
  provider: mock
  timeout: 5s
  max-retries: 1

# Test external API configuration
external:
  kraken:
    base-url: http://localhost:8089  # WireMock server
    timeout: 2s
    max-retries: 1
  alternative:
    base-url: http://localhost:8089
    timeout: 2s
    max-retries: 1
  coingecko:
    base-url: http://localhost:8089
    timeout: 2s
    max-retries: 1

# Management endpoints for tests
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
