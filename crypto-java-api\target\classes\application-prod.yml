# Production Profile Configuration
spring:
  # Database Configuration for Production
  r2dbc:
    pool:
      initial-size: 20
      max-size: 50
      max-idle-time: 1800s
      max-acquire-time: 10s
      max-life-time: 3600s
  
  # Redis Configuration for Production (Upstash)
  data:
    redis:
      ssl:
        enabled: true
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 50
          max-idle: 20
          min-idle: 10
          max-wait: 5000ms

# Server Configuration for Production
server:
  port: 8080
  compression:
    enabled: true
    min-response-size: 512
  http2:
    enabled: true
  forward-headers-strategy: native

# Logging Configuration for Production
logging:
  level:
    com.cryptoapp: INFO
    org.springframework.r2dbc: WARN
    org.springframework.data.r2dbc: WARN
    org.springframework.web.reactive: WARN
    reactor.netty: WARN
    org.springframework.security: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    name: /var/log/crypto-java-api/application.log
    max-size: 100MB
    max-history: 30

# Management endpoints for production (restricted)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  security:
    enabled: true

# Performance settings for production
performance:
  max-concurrent-tasks: 10
  request-timeout: 60s
  connection-timeout: 10s
  
  cache:
    memory:
      max-size: 50000
      expire-after-write: 600s
      expire-after-access: 1200s
    
    redis:
      default-ttl: 7200s

# Circuit breaker settings for production (strict)
resilience4j:
  circuitbreaker:
    instances:
      llm-provider:
        sliding-window-size: 20
        minimum-number-of-calls: 10
        failure-rate-threshold: 40
        wait-duration-in-open-state: 60s
        slow-call-duration-threshold: 15s
      external-api:
        sliding-window-size: 30
        minimum-number-of-calls: 15
        failure-rate-threshold: 50
        wait-duration-in-open-state: 120s
        slow-call-duration-threshold: 8s
  
  retry:
    instances:
      llm-provider:
        max-attempts: 5
        wait-duration: 2s
        exponential-backoff-multiplier: 2
      external-api:
        max-attempts: 5
        wait-duration: 1s
        exponential-backoff-multiplier: 2

# Security Configuration for Production
security:
  require-ssl: true
  jwt:
    secret: ${JWT_SECRET}
    expiration: 86400000  # 24 hours
  
  rate-limiting:
    enabled: true
    requests-per-minute: 100
    burst-capacity: 200

# CORS for production (restrictive)
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS}
  allowed-methods: GET,POST,PUT,DELETE
  allow-credentials: false
  max-age: 86400

# Monitoring and observability
monitoring:
  tracing:
    enabled: true
    sampling-rate: 0.1
  
  metrics:
    jvm:
      enabled: true
    system:
      enabled: true
    custom:
      enabled: true
