package com.cryptoapp.infrastructure.scheduler;

import com.cryptoapp.domain.RssFeedRaw;
import com.cryptoapp.infrastructure.repository.RssFeedRawRepository;
import com.cryptoapp.port.out.RssFeedPort;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
// Removed unused import
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Scheduler for Stage 1: Raw RSS Data Collection and Cleaning
 * 
 * This scheduler is responsible for:
 * 1. Fetching RSS feeds from configured sources
 * 2. Cleaning and normalizing the raw data
 * 3. Preventing duplicates using GUID and content hash
 * 4. Saving cleaned data to rss_feed_raw table for later LLM processing
 * 
 * Runs on application startup and then on a scheduled interval.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RssRawDataCollectionScheduler {

    private final RssFeedPort rssFeedPort;
    private final RssFeedRawRepository rssFeedRawRepository;
    private final ObjectMapper objectMapper;
    private final DatabaseClient databaseClient;

    @Value("${jobs.rss-stage1.enabled:true}")
    private boolean enabled;

    @Value("${jobs.rss-stage1.batch-size:50}")
    private int batchSize;

    @Value("${jobs.rss-stage1.startup-delay:30}")
    private int startupDelaySeconds;

    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger duplicateCount = new AtomicInteger(0);
    private final AtomicInteger errorCount = new AtomicInteger(0);

    /**
     * Run RSS data collection on application startup (after delay)
     */
    @EventListener(ApplicationReadyEvent.class)
    public void runOnStartup() {
        if (!enabled) {
            log.info("RSS Stage 1 (Raw Data Collection) is disabled");
            return;
        }

        log.info("Scheduling RSS Stage 1 (Raw Data Collection) to run in {} seconds after startup", startupDelaySeconds);
        
        Mono.delay(Duration.ofSeconds(startupDelaySeconds))
            .then(collectRawRssData())
            .subscribe(
                result -> log.info("Startup RSS Stage 1 completed: {}", result),
                error -> log.error("Startup RSS Stage 1 failed", error)
            );
    }

    /**
     * Scheduled RSS data collection (every 5 minutes by default)
     */
    @Scheduled(fixedDelayString = "${jobs.rss-stage1.interval:300000}")
    public void scheduledRssDataCollection() {
        if (!enabled) {
            return;
        }

        log.info("Starting scheduled RSS Stage 1 (Raw Data Collection)");
        
        collectRawRssData()
            .subscribe(
                result -> log.info("Scheduled RSS Stage 1 completed: {}", result),
                error -> log.error("Scheduled RSS Stage 1 failed", error)
            );
    }

    /**
     * Main method to collect and process raw RSS data
     */
    public Mono<String> collectRawRssData() {
        long startTime = System.currentTimeMillis();
        processedCount.set(0);
        duplicateCount.set(0);
        errorCount.set(0);

        log.info("Starting RSS Stage 1: Raw data collection with batch size {}", batchSize);

        return rssFeedPort.fetchArticlesFromAllFeeds(batchSize)
            .flatMap(this::processRssArticle)
            .collectList()
            .map(results -> {
                long duration = System.currentTimeMillis() - startTime;
                String summary = String.format(
                    "RSS Stage 1 completed in %dms: %d processed, %d duplicates, %d errors",
                    duration, processedCount.get(), duplicateCount.get(), errorCount.get()
                );
                log.info(summary);
                return summary;
            })
            .onErrorResume(error -> {
                log.error("RSS Stage 1 failed", error);
                return Mono.just("RSS Stage 1 failed: " + error.getMessage());
            });
    }

    /**
     * Process a single RSS article
     */
    private Mono<String> processRssArticle(RssFeedPort.RssArticle rssArticle) {
        try {
            // Validate required fields
            if (rssArticle.getGuid() == null || rssArticle.getGuid().trim().isEmpty()) {
                log.warn("Skipping article with missing GUID: {}", rssArticle.getTitle());
                errorCount.incrementAndGet();
                return Mono.just("SKIPPED_NO_GUID");
            }

            // Check for duplicates first
            return rssFeedRawRepository.existsByGuid(rssArticle.getGuid())
                .flatMap(exists -> {
                    if (exists) {
                        log.debug("Duplicate article found, skipping: {}", rssArticle.getGuid());
                        duplicateCount.incrementAndGet();
                        return Mono.just("DUPLICATE");
                    }

                    // Clean and normalize the data
                    return cleanAndSaveRssArticle(rssArticle);
                })
                .onErrorResume(error -> {
                    log.error("Error processing RSS article {}: {}", rssArticle.getGuid(), error.getMessage());
                    errorCount.incrementAndGet();
                    return Mono.just("ERROR");
                });

        } catch (Exception e) {
            log.error("Unexpected error processing RSS article", e);
            errorCount.incrementAndGet();
            return Mono.just("ERROR");
        }
    }

    /**
     * Clean, normalize and save RSS article data
     */
    private Mono<String> cleanAndSaveRssArticle(RssFeedPort.RssArticle rssArticle) {
        try {
            // Clean and normalize data
            String cleanTitle = cleanText(rssArticle.getTitle(), 1000);
            String cleanDescription = cleanText(rssArticle.getDescription(), 5000);
            String cleanContent = cleanText(rssArticle.getContent(), 50000);
            String cleanAuthor = cleanText(rssArticle.getAuthor(), 255);
            String cleanCategory = cleanText(rssArticle.getCategory(), 100);

            // Generate content hash for deduplication
            String contentHash = generateContentHash(cleanTitle, cleanDescription);

            // Create raw RSS data for storage
            Map<String, Object> rawRssData = createRawRssDataMap(rssArticle);
            Map<String, Object> normalizedData = createNormalizedDataMap(
                cleanTitle, cleanDescription, cleanContent, cleanAuthor, cleanCategory
            );

            // Build the entity
            RssFeedRaw rawEntity = RssFeedRaw.builder()
                .guid(rssArticle.getGuid().trim())
                .source(rssArticle.getSource() != null ? rssArticle.getSource() : "Unknown")
                .sourceUrl(rssArticle.getLink() != null ? rssArticle.getLink() : "")
                .title(cleanTitle)
                .description(cleanDescription)
                .content(cleanContent)
                .author(cleanAuthor)
                .category(cleanCategory)
                .imageUrl(rssArticle.getImageUrl())
                .link(rssArticle.getLink())
                .publishedDate(rssArticle.getPublishedDate())
                .fetchedAt(LocalDateTime.now())
                .processingStatus(RssFeedRaw.ProcessingStatus.PENDING)
                .processingAttempts(0)
                .feedPriority(determineFeedPriority(rssArticle.getSource()))
                .contentHash(contentHash)
                .rawRssData(com.cryptoapp.infrastructure.json.Json.of(objectMapper.writeValueAsString(rawRssData)))
                .normalizedData(com.cryptoapp.infrastructure.json.Json.of(objectMapper.writeValueAsString(normalizedData)))
                .build();

            // Save to database
            return rssFeedRawRepository.save(rawEntity)
                .map(saved -> {
                    log.debug("Saved raw RSS article: {} (ID: {})", saved.getTitle(), saved.getId());
                    processedCount.incrementAndGet();
                    return "PROCESSED";
                })
                .retryWhen(Retry.backoff(2, Duration.ofMillis(500)))
                .onErrorResume(error -> {
                    log.error("Failed to save RSS article {}: {}", rssArticle.getGuid(), error.getMessage());
                    errorCount.incrementAndGet();
                    return Mono.just("SAVE_ERROR");
                });

        } catch (Exception e) {
            log.error("Error cleaning and saving RSS article", e);
            errorCount.incrementAndGet();
            return Mono.just("PROCESSING_ERROR");
        }
    }

    /**
     * Clean and normalize text content
     */
    private String cleanText(String text, int maxLength) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }

        String cleaned = text.trim()
            .replaceAll("\\s+", " ")                    // Multiple spaces to single space
            .replaceAll("[\\r\\n\\t]+", " ")            // Line breaks and tabs to space
            .replaceAll("\\p{Cntrl}", "")               // Remove control characters
            .replaceAll("&nbsp;", " ")                  // HTML non-breaking space
            .replaceAll("&amp;", "&")                   // HTML ampersand
            .replaceAll("&lt;", "<")                    // HTML less than
            .replaceAll("&gt;", ">")                    // HTML greater than
            .replaceAll("&quot;", "\"")                 // HTML quote
            .replaceAll("&#39;", "'")                   // HTML apostrophe
            .replaceAll("<[^>]+>", "")                  // Remove HTML tags
            .trim();

        // Truncate if too long
        if (cleaned.length() > maxLength) {
            cleaned = cleaned.substring(0, maxLength - 3) + "...";
        }

        return cleaned.isEmpty() ? null : cleaned;
    }

    /**
     * Generate SHA-256 content hash for deduplication
     */
    private String generateContentHash(String title, String description) {
        try {
            String content = (title != null ? title : "") + "|" + (description != null ? description : "");
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(content.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            log.warn("Failed to generate content hash, using fallback", e);
            return String.valueOf((title + description).hashCode());
        }
    }

    /**
     * Create raw RSS data map for JSON storage
     */
    private Map<String, Object> createRawRssDataMap(RssFeedPort.RssArticle rssArticle) {
        Map<String, Object> rawData = new HashMap<>();
        rawData.put("originalTitle", rssArticle.getTitle());
        rawData.put("originalDescription", rssArticle.getDescription());
        rawData.put("originalContent", rssArticle.getContent());
        rawData.put("originalAuthor", rssArticle.getAuthor());
        rawData.put("originalCategory", rssArticle.getCategory());
        rawData.put("customFields", rssArticle.getCustomFields());
        rawData.put("tags", rssArticle.getTags());
        rawData.put("fetchedAt", LocalDateTime.now().toString());
        return rawData;
    }

    /**
     * Create normalized data map for JSON storage
     */
    private Map<String, Object> createNormalizedDataMap(String title, String description, 
                                                       String content, String author, String category) {
        Map<String, Object> normalizedData = new HashMap<>();
        normalizedData.put("cleanedTitle", title);
        normalizedData.put("cleanedDescription", description);
        normalizedData.put("cleanedContent", content);
        normalizedData.put("cleanedAuthor", author);
        normalizedData.put("cleanedCategory", category);
        normalizedData.put("normalizedAt", LocalDateTime.now().toString());
        return normalizedData;
    }

    /**
     * Determine feed priority based on source
     */
    private Integer determineFeedPriority(String source) {
        if (source == null) return 5;
        
        return switch (source.toLowerCase()) {
            case "coindesk", "cointelegraph" -> 1;          // Highest priority
            case "bitcoin magazine", "decrypt" -> 2;        // High priority
            case "cryptonews", "cryptopotato" -> 3;         // Medium priority
            case "cryptoslate", "beincrypto" -> 4;          // Lower priority
            default -> 5;                                   // Default priority
        };
    }

    /**
     * Get processing statistics
     */
    public Map<String, Integer> getProcessingStats() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("processed", processedCount.get());
        stats.put("duplicates", duplicateCount.get());
        stats.put("errors", errorCount.get());
        return stats;
    }
}
