spring:
  application:
    name: crypto-java-api
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  # Database Configuration (R2DBC for reactive access)
  r2dbc:
    url: ${R2DBC_URL:r2dbc:postgresql://ep-orange-pond-a27eado1-pooler.eu-central-1.aws.neon.tech:5432/neondb?sslmode=require&project=ep-orange-pond-a27eado1}
    username: ${DB_USERNAME:neondb_owner}
    password: ${DB_PASSWORD:npg_okyEf7ZOJ1Ui}
    pool:
      initial-size: ${DB_POOL_SIZE:15}
      max-size: ${DB_MAX_OVERFLOW:25}
      max-idle-time: ${DB_POOL_RECYCLE:3600s}
      max-acquire-time: ${DB_POOL_TIMEOUT:30s}
      max-life-time: 7200s
      validation-query: SELECT 1
  
  # Flyway Configuration (JDBC for migrations)
  flyway:
    url: ${FLYWAY_URL:******************************************************************************************************************************}
    user: ${DB_USERNAME:neondb_owner}
    password: ${DB_PASSWORD:npg_okyEf7ZOJ1Ui}
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
  
  # Redis Configuration (Upstash Redis)
  data:
    redis:
      url: ${REDIS_URL:redis://localhost:6379}
      ssl:
        enabled: ${REDIS_SSL_ENABLED:false}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms
  
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null
  
  # WebFlux Configuration
  webflux:
    base-path: /api
  
  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${JWT_ISSUER_URI:}
  
  # Cache Configuration
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=300s

# Server Configuration
server:
  port: ${SERVER_PORT:8080}
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain
    min-response-size: 1024
  http2:
    enabled: true

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,cache,scheduledtasks
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
  health:
    redis:
      enabled: true
    r2dbc:
      enabled: true

# Logging Configuration
logging:
  level:
    com.cryptoapp: ${LOG_LEVEL:DEBUG}
    org.springframework.r2dbc: ${DB_LOG_LEVEL:WARN}
    org.springframework.data.r2dbc: ${DB_LOG_LEVEL:WARN}
    io.r2dbc.postgresql: ${DB_LOG_LEVEL:WARN}
    org.springframework.web.reactive: ${WEB_LOG_LEVEL:INFO}
    reactor.netty: ${NETTY_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:logs/crypto-java-api.log}

# LLM Configuration
llm:
  provider: ${LLM_PROVIDER:local}
  timeout: 30s
  max-retries: 3
  openai:
    api-key: ${OPENAI_API_KEY:}
    model: ${OPENAI_MODEL:gpt-4-turbo}
    base-url: https://api.openai.com/v1
    temperature: 0.7
  gemini:
    api-key: ${GEMINI_API_KEY:}
    model: gemini-pro
    base-url: https://generativelanguage.googleapis.com/v1beta
    temperature: 0.7
  deepseek:
    api-key: ${DEEPSEEK_API_KEY:}
    model: ${DEEPSEEK_MODEL:deepseek-chat}
    base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com}
    temperature: 0.7
  local:
    base-url: ${LM_STUDIO_BASE_URL:http://localhost:1234/v1}
    model: ${LM_STUDIO_MODEL:default}
    api-key: lm-studio
    temperature: 0.7
    enabled: ${LLM_LOCAL_ENABLED:true}

# External API Configuration
external:
  api:
    provider: ${EXTERNAL_API_PROVIDER:alternative}  # Options: kraken, coingecko, alternative
  kraken:
    base-url: ${KRAKEN_API_URL:https://api.kraken.com}
    timeout: 10s
    max-retries: 3
  alternative:
    base-url: ${ALTERNATIVE_API_URL:https://api.alternative.me/v1/ticker/}
    timeout: 10s
    max-retries: 3
  coingecko:
    base-url: ${COINGECKO_API_URL:https://api.coingecko.com/api/v3}
    timeout: 10s
    max-retries: 3

# Scheduling Configuration (in milliseconds for Spring)
scheduling:
  enabled: true
  article-sync-interval: ${ARTICLE_SYNC_INTERVAL:3600000}  # 1 hour
  article-sync-limit: ${ARTICLE_SYNC_LIMIT:20}
  crypto-sync-interval: ${CRYPTO_SYNC_INTERVAL:60000}      # 1 minute
  ohlc-cache-warm-interval: ${OHLC_CACHE_WARM_INTERVAL:300000}  # 5 minutes
  cache-cleanup-interval: ${CACHE_CLEANUP_INTERVAL:3600000}     # 1 hour

# Performance Configuration
performance:
  max-concurrent-tasks: ${MAX_CONCURRENT_TASKS:5}
  request-timeout: 30s
  connection-timeout: 5s
  
  # Cache Configuration
  cache:
    ttl:
      market-data: ${MARKET_DATA_CACHE_TTL:60}  # seconds
      articles: 3600      # 1 hour
      ohlc: 300          # 5 minutes
      featured-articles: 1800  # 30 minutes
    
    # Memory cache settings
    memory:
      max-size: 10000
      expire-after-write: 300s
      expire-after-access: 600s
    
    # Redis cache settings
    redis:
      default-ttl: 3600s
      key-prefix: "crypto-api:"

# Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      llm-provider:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 10s
      external-api:
        register-health-indicator: true
        sliding-window-size: 20
        minimum-number-of-calls: 10
        permitted-number-of-calls-in-half-open-state: 5
        wait-duration-in-open-state: 60s
        failure-rate-threshold: 60
        slow-call-rate-threshold: 60
        slow-call-duration-threshold: 5s
  
  retry:
    instances:
      llm-provider:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
      external-api:
        max-attempts: 3
        wait-duration: 500ms
        exponential-backoff-multiplier: 2

# CORS Configuration
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600

# RSS Feed Configuration
rss:
  feeds: ${RSS_FEEDS:https://cointelegraph.com/rss,https://decrypt.co/feed,https://www.coindesk.com/arc/outboundfeeds/rss/,https://bitcoinmagazine.com/.rss/full/,https://www.theblockcrypto.com/rss.xml,https://cryptonews.com/news/feed/,https://cryptopotato.com/feed/,https://cryptoslate.com/feed/,https://beincrypto.com/feed/,https://ambcrypto.com/feed/}
  timeout: 30s
  max-retries: 3
  user-agent: "CryptoApp/1.0"
  # Feed-specific configuration
  feed-priorities:
    "cointelegraph.com": HIGH
    "coindesk.com": HIGH
    "decrypt.co": HIGH
    "bitcoinmagazine.com": MEDIUM
    "theblockcrypto.com": HIGH
    "cryptonews.com": MEDIUM
    "cryptopotato.com": MEDIUM
    "cryptoslate.com": MEDIUM
    "beincrypto.com": LOW
    "ambcrypto.com": LOW
  # Processing limits per priority
  limits:
    high-priority: 15
    medium-priority: 10
    low-priority: 5
  # Duplicate detection configuration
  duplicate-detection:
    enabled: true
    check-guid: true
    check-title-similarity: true
    title-similarity-threshold: 0.85
    check-url-similarity: true
    url-similarity-threshold: 0.90

# Security Configuration
security:
  jwt:
    secret: ${JWT_SECRET:default-secret-key-change-in-production-environment}
    expiration: ${JWT_EXPIRATION:24h}
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:7d}
    issuer: ${JWT_ISSUER:crypto-app}
    enabled: ${JWT_ENABLED:true}
  demo:
    enabled: ${DEMO_MODE_ENABLED:true}
  registration:
    enabled: ${REGISTRATION_ENABLED:false}

# Cache Configuration
cache:
  in-memory:
    max-size: ${CACHE_IN_MEMORY_MAX_SIZE:10000}
    expire-after-write: ${CACHE_IN_MEMORY_EXPIRE:5m}
  redis:
    default-ttl: ${CACHE_REDIS_DEFAULT_TTL:1h}
    key-prefix: ${CACHE_REDIS_KEY_PREFIX:crypto-cache}
  compression:
    enabled: ${CACHE_COMPRESSION_ENABLED:true}
  warming:
    enabled: ${CACHE_WARMING_ENABLED:true}

# Background Jobs Configuration
jobs:
  # Cryptocurrency data synchronization
  crypto-sync:
    enabled: ${JOBS_CRYPTO_SYNC_ENABLED:true}

  # Cache optimization and warming
  cache-optimization:
    enabled: ${JOBS_CACHE_OPTIMIZATION_ENABLED:true}

  # Data cleanup and maintenance
  data-cleanup:
    enabled: ${JOBS_DATA_CLEANUP_ENABLED:true}

  # Clean Two-Stage RSS Article Processing
  rss-stage1:
    enabled: ${JOBS_RSS_STAGE1_ENABLED:true}
    batch-size: ${JOBS_RSS_STAGE1_BATCH_SIZE:150}

  rss-stage2:
    enabled: ${JOBS_RSS_STAGE2_ENABLED:true}
    batch-size: ${JOBS_RSS_STAGE2_BATCH_SIZE:50}

  # Startup processing for immediate data availability
  startup-processing:
    enabled: ${JOBS_STARTUP_PROCESSING_ENABLED:true}

  # Legacy configurations (disabled in favor of clean two-stage processing)
  article-sync:
    enabled: false  # Replaced by rss-stage1 and rss-stage2
  llm-processing:
    enabled: false  # Replaced by rss-stage2

  # Distributed locking (if needed for other services)
  distributed-locking:
    enabled: ${JOBS_DISTRIBUTED_LOCKING_ENABLED:false}
  lock-timeout: ${JOBS_LOCK_TIMEOUT:5m}
  lock-ttl: ${JOBS_LOCK_TTL:10m}

# Notifications Configuration
notifications:
  redis:
    channel-prefix: ${NOTIFICATIONS_REDIS_CHANNEL_PREFIX:crypto-notifications}
  websocket:
    max-connections: ${NOTIFICATIONS_WEBSOCKET_MAX_CONNECTIONS:1000}
  rate-limit:
    messages-per-minute: ${NOTIFICATIONS_RATE_LIMIT_MESSAGES_PER_MINUTE:60}

# External API Configuration
external-api:
  coingecko:
    base-url: ${COINGECKO_API_URL:https://api.coingecko.com/api/v3}
    api-key: ${COINGECKO_API_KEY:}
    timeout: ${COINGECKO_TIMEOUT:30s}
    max-retries: ${COINGECKO_MAX_RETRIES:3}
    rate-limit-delay: ${COINGECKO_RATE_LIMIT_DELAY:1s}

# Popular cryptocurrency symbols for cache warming
crypto:
  popular-symbols:
    - BTC
    - ETH
    - SOL
    - XRP
    - ADA
    - DOGE
    - AVAX
    - LINK
    - MATIC
    - DOT

  # OHLC intervals for cache warming (in minutes)
  popular-intervals:
    - 5
    - 15
    - 60
    - 240
    - 1440

  # Batch processing configuration
  batch:
    size: ${CRYPTO_BATCH_SIZE:50}                    # Number of cryptocurrencies to process in one batch
    max-concurrent-batches: ${CRYPTO_MAX_CONCURRENT_BATCHES:3}  # Maximum concurrent batch operations
    buffer-timeout: ${CRYPTO_BUFFER_TIMEOUT:5s}      # Timeout for buffering operations
    backpressure-buffer-size: ${CRYPTO_BACKPRESSURE_BUFFER:1000}  # Backpressure buffer size

  # Adaptive scheduling configuration
  scheduling:
    base-interval: ${CRYPTO_BASE_INTERVAL:300000}    # Base sync interval in milliseconds (5 minutes)
    high-volatility-interval: ${CRYPTO_HIGH_VOLATILITY_INTERVAL:120000}  # High volatility interval (2 minutes)
    low-volatility-interval: ${CRYPTO_LOW_VOLATILITY_INTERVAL:900000}   # Low volatility interval (15 minutes)
    volatility-threshold: ${CRYPTO_VOLATILITY_THRESHOLD:3.0}  # Volatility threshold percentage (more sensitive)
    volatility-check-window: ${CRYPTO_VOLATILITY_WINDOW:24}   # Hours to check for volatility calculation

  # Priority-based processing configuration
  priority:
    high-priority-market-cap-threshold: ${CRYPTO_HIGH_PRIORITY_MARKET_CAP:1000000000}  # $1B market cap
    high-priority-volume-threshold: ${CRYPTO_HIGH_PRIORITY_VOLUME:100000000}           # $100M 24h volume
    high-priority-symbols:  # Always high priority regardless of market cap/volume
      - BTC
      - ETH
      - USDT
      - BNB
      - SOL
    medium-priority-rank-threshold: ${CRYPTO_MEDIUM_PRIORITY_RANK:100}  # Top 100 by rank

  # Memory and performance optimization
  performance:
    max-memory-buffer-size: ${CRYPTO_MAX_MEMORY_BUFFER:500}    # Maximum items in memory buffer
    processing-concurrency: ${CRYPTO_PROCESSING_CONCURRENCY:10}  # Concurrent processing limit
    rate-limit-per-second: ${CRYPTO_RATE_LIMIT:20}             # Rate limit for external API calls
    circuit-breaker-threshold: ${CRYPTO_CIRCUIT_BREAKER_THRESHOLD:5}  # Circuit breaker failure threshold
