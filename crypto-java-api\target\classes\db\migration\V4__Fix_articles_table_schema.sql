-- Fix articles table schema to match the expected Java entity structure
-- This migration ensures the articles table has all the required columns

-- First, check if we need to add missing columns
-- Add missing columns if they don't exist

-- Add source column if missing
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'source') THEN
        ALTER TABLE articles ADD COLUMN source VARCHAR(255);
    END IF;
END $$;

-- Add read_time column if missing
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'read_time') THEN
        ALTER TABLE articles ADD COLUMN read_time VARCHAR(50);
    END IF;
END $$;

-- Add body column if missing (JSONB for structured content)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'body') THEN
        ALTER TABLE articles ADD COLUMN body JSONB;
    END IF;
END $$;

-- Add key_takeaways column if missing (JSONB array)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'key_takeaways') THEN
        ALTER TABLE articles ADD COLUMN key_takeaways JSONB;
    END IF;
END $$;

-- Add related_links column if missing (JSONB array)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'related_links') THEN
        ALTER TABLE articles ADD COLUMN related_links JSONB;
    END IF;
END $$;

-- Add sections column if missing (JSONB array of structured sections)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'sections') THEN
        ALTER TABLE articles ADD COLUMN sections JSONB;
    END IF;
END $$;

-- Add tags column if missing (JSONB array)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'tags') THEN
        ALTER TABLE articles ADD COLUMN tags JSONB;
    END IF;
END $$;

-- Add highlighted_coins column if missing (JSONB array)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'highlighted_coins') THEN
        ALTER TABLE articles ADD COLUMN highlighted_coins JSONB;
    END IF;
END $$;

-- Add tone column if missing
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'tone') THEN
        ALTER TABLE articles ADD COLUMN tone VARCHAR(100);
    END IF;
END $$;

-- Add summaries column if missing (JSONB array of summary points)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'summaries') THEN
        ALTER TABLE articles ADD COLUMN summaries JSONB;
    END IF;
END $$;

-- Add raw_llm_response column if missing
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'articles' AND column_name = 'raw_llm_response') THEN
        ALTER TABLE articles ADD COLUMN raw_llm_response TEXT;
    END IF;
END $$;

-- Fix hot_score column type if it's DECIMAL instead of INTEGER
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'articles' AND column_name = 'hot_score' 
               AND data_type = 'numeric') THEN
        ALTER TABLE articles ALTER COLUMN hot_score TYPE INTEGER USING hot_score::INTEGER;
        ALTER TABLE articles ADD CONSTRAINT chk_hot_score_range CHECK (hot_score >= 0 AND hot_score <= 100);
    END IF;
END $$;

-- Ensure updated_at column exists (rename last_updated if needed)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'articles' AND column_name = 'last_updated') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'articles' AND column_name = 'updated_at') THEN
        ALTER TABLE articles RENAME COLUMN last_updated TO updated_at;
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'articles' AND column_name = 'updated_at') THEN
        ALTER TABLE articles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    END IF;
END $$;

-- Create missing indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_articles_source ON articles(source);
CREATE INDEX IF NOT EXISTS idx_articles_tone ON articles(tone);
CREATE INDEX IF NOT EXISTS idx_articles_updated_at ON articles(updated_at);

-- Add comments for the new columns
COMMENT ON COLUMN articles.body IS 'Structured article content in JSON format';
COMMENT ON COLUMN articles.key_takeaways IS 'JSON array of key takeaway points';
COMMENT ON COLUMN articles.related_links IS 'JSON array of related article links';
COMMENT ON COLUMN articles.sections IS 'JSON array of structured article sections';
COMMENT ON COLUMN articles.tags IS 'JSON array of article tags';
COMMENT ON COLUMN articles.highlighted_coins IS 'JSON array of cryptocurrency symbols mentioned';
COMMENT ON COLUMN articles.tone IS 'AI-analyzed tone of the article';
COMMENT ON COLUMN articles.summaries IS 'JSON array of AI-generated summary points';
COMMENT ON COLUMN articles.raw_llm_response IS 'Raw response from LLM processing';
